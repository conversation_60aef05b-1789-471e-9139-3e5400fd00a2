#!/usr/bin/env python3
"""
Smart US Template Generator
Prevents recurring CSS/JS reference and database issues
"""

import os
import shutil
import re
from pathlib import Path

def create_new_us(source_us, target_us, base_dir="D:/Dr.Resume"):
    """
    Create a new US from existing US with proper reference updates
    
    Args:
        source_us: Source US number (e.g., "08")
        target_us: Target US number (e.g., "09") 
        base_dir: Base directory path
    """
    
    source_path = Path(base_dir) / f"us{source_us}"
    target_path = Path(base_dir) / f"us{target_us}"
    
    print(f"🚀 Creating US-{target_us} from US-{source_us}")
    
    # Step 1: Copy directory structure
    if target_path.exists():
        print(f"⚠️  US-{target_us} already exists, removing...")
        shutil.rmtree(target_path)
    
    print(f"📁 Copying {source_path} to {target_path}")
    shutil.copytree(source_path, target_path)
    
    # Step 2: Update all file references
    update_file_references(target_path, source_us, target_us)
    
    # Step 3: Rename files
    rename_files(target_path, source_us, target_us)
    
    print(f"✅ US-{target_us} created successfully!")
    print(f"📍 Location: {target_path}")
    
    return target_path

def update_file_references(us_path, source_us, target_us):
    """Update all file references from source US to target US"""
    
    print(f"🔄 Updating file references from US-{source_us} to US-{target_us}")
    
    # File patterns to update
    patterns = [
        # CSS references
        (rf'/static/css/us{source_us}_styles\.css', f'/static/css/us{target_us}_styles.css'),
        # JS references  
        (rf'/static/js/us{source_us}_([a-z_]+)\.js', rf'/static/js/us{target_us}_\1.js'),
        # Template references
        (rf"'us{source_us}_([a-z_]+)\.html'", rf"'us{target_us}_\1.html'"),
        (rf'"us{source_us}_([a-z_]+)\.html"', rf'"us{target_us}_\1.html"'),
        # Service names
        (rf'Dr\. Resume US-{source_us}', f'Dr. Resume US-{target_us}'),
        # Comments and logs
        (rf'US-{source_us}', f'US-{target_us}'),
        (rf'us{source_us}', f'us{target_us}'),
    ]
    
    # File extensions to process
    extensions = ['.html', '.js', '.py', '.css', '.md']
    
    for root, dirs, files in os.walk(us_path):
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                file_path = Path(root) / file
                update_file_content(file_path, patterns)

def update_file_content(file_path, patterns):
    """Update content of a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply all patterns
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ Updated: {file_path.name}")
            
    except Exception as e:
        print(f"  ❌ Error updating {file_path}: {e}")

def rename_files(us_path, source_us, target_us):
    """Rename files from source US to target US"""
    
    print(f"📝 Renaming files from US-{source_us} to US-{target_us}")
    
    for root, dirs, files in os.walk(us_path):
        for file in files:
            if f"us{source_us}_" in file:
                old_path = Path(root) / file
                new_name = file.replace(f"us{source_us}_", f"us{target_us}_")
                new_path = Path(root) / new_name
                
                old_path.rename(new_path)
                print(f"  📄 {file} → {new_name}")

if __name__ == "__main__":
    # Create US-09 from US-08
    create_new_us("08", "09")
