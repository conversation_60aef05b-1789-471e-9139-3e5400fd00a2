# US-01: User Registration System 🩺

## 🎯 What We're Building
A complete user registration system that matches your exact UI design with:
- **Backend API**: Flask endpoint for user registration with validation
- **Frontend UI**: Exact purple-themed design matching your examples
- **Database**: PostgreSQL with proper user model and indexing
- **Security**: Password hashing, email validation, CORS protection
- **Testing**: Comprehensive test suite with pytest

## 📁 Project Structure
```
us01/
├── backend/
│   ├── app.py                    # 🚀 Main Flask application
│   ├── models.py                 # 📊 User database model with validation
│   ├── routes/
│   │   └── us01_auth_routes.py   # 🔐 Registration & email check endpoints
│   ├── config.py                 # ⚙️ Database & app configuration
│   ├── requirements.txt          # 📦 Python dependencies
│   └── .env.example              # 🔧 Environment variables template
├── frontend/
│   ├── us01_landing.html         # 🏠 Landing page (exact UI match)
│   ├── us01_register.html        # 📝 Registration form (exact UI match)
│   ├── us01_login.html           # 🔐 Login page (placeholder for US-02)
│   └── static/
│       ├── css/
│       │   └── us01_styles.css   # 🎨 Purple theme styling
│       └── js/
│           └── us01_register.js  # ⚡ Registration logic & validation
├── database/
│   └── us01_init.sql             # 🗄️ Database initialization script
├── tests/
│   └── test_us01_registration.py # 🧪 Comprehensive test suite
└── README_US01.md                # 📖 This documentation
```

## 🔧 Technology Stack
- **Backend**: Flask 2.3.3, SQLAlchemy 3.0.5, PostgreSQL
- **Frontend**: HTML5, CSS3, Vanilla JavaScript (no frameworks)
- **Security**: Werkzeug password hashing, CORS protection
- **Testing**: pytest with Flask testing utilities
- **Database**: PostgreSQL with proper indexing and triggers

## 🚀 Complete User Flow

### 1. Landing Page Experience
1. User visits `http://localhost:5000` 
2. Sees purple gradient welcome page with "Dr. Resume" branding
3. Two options: "👤 Create Account" or "🔐 Sign In"
4. Clicks "Create Account" → navigates to registration

### 2. Registration Process
1. User fills registration form with validation:
   - First Name (required)
   - Last Name (required) 
   - Email (validated format + uniqueness check)
   - Password (8+ characters, confirmation required)
   - Terms & conditions agreement
2. Real-time validation provides immediate feedback
3. Form submits via AJAX to `/api/register`
4. Success → redirects to login page after 2 seconds
5. Error → shows specific validation messages

### 3. Backend Processing
1. `POST /api/register` receives JSON data
2. Server-side validation (email format, password strength, duplicates)
3. Password hashed with Werkzeug's secure methods
4. User saved to PostgreSQL with timestamps
5. JSON response with success/error details

## 📊 Database Schema
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Optimized with email index for fast lookups
CREATE INDEX idx_users_email ON users(email);
```

## 🔧 Quick Setup & Testing

### 1. Install Dependencies
```bash
cd us01/backend
pip install -r requirements.txt
```

### 2. Setup Database
```bash
# Create PostgreSQL database
createdb dr_resume_dev

# Initialize tables
psql dr_resume_dev < ../database/us01_init.sql
```

### 3. Configure Environment
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your database credentials
DATABASE_URL=postgresql://username:password@localhost/dr_resume_dev
SECRET_KEY=your-secret-key-here
```

### 4. Run Application
```bash
python app.py
```

### 5. Test the System
- **Landing**: http://localhost:5000
- **Register**: http://localhost:5000/register  
- **Login**: http://localhost:5000/login
- **Health**: http://localhost:5000/health

### 6. Run Tests
```bash
cd ../tests
pytest test_us01_registration.py -v
```

## 🎨 UI Features Implemented

✅ **Landing Page** - Exact match to your design
- Purple gradient background (#7c3aed to #a855f7)
- "Dr. Resume" branding with medical emoji
- "Create Account" and "Sign In" buttons
- Responsive design

✅ **Registration Form** - Exact match to your design  
- All required fields with proper labels
- Password visibility toggle
- Real-time validation feedback
- Terms & conditions checkbox
- Success/error alert messages
- Loading states during submission

✅ **Login Page** - Placeholder for US-02
- Consistent styling with registration
- Ready for US-02 implementation

## 🧪 Testing Coverage

✅ **Registration Tests**
- Successful registration flow
- Duplicate email handling
- Invalid email format validation
- Password mismatch detection
- Short password rejection
- Missing required fields
- Email existence checking

✅ **Model Tests**
- Password hashing verification
- Email validation logic
- Password strength validation
- User serialization (excluding sensitive data)

## 🔒 Security Features

✅ **Password Security**
- Werkzeug password hashing (scrypt algorithm)
- Minimum 8 character requirement
- Password confirmation validation

✅ **Email Security**
- Format validation with regex
- Uniqueness enforcement at database level
- Case-insensitive email storage

✅ **API Security**
- CORS protection for cross-origin requests
- JSON-only API endpoints
- Input sanitization and validation
- Error handling without sensitive data exposure

## 📝 API Endpoints

### POST /api/register
Register a new user account
```json
{
  "first_name": "John",
  "last_name": "Doe", 
  "email": "<EMAIL>",
  "password": "securepassword",
  "confirm_password": "securepassword"
}
```

**Success Response (201)**:
```json
{
  "success": true,
  "message": "Account created successfully! Please sign in.",
  "user": {
    "id": 1,
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "created_at": "2025-07-25T10:30:00"
  }
}
```

**Error Response (400)**:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    "Email already registered",
    "Password must be at least 8 characters long"
  ]
}
```

### POST /api/check-email
Check if email is already registered
```json
{
  "email": "<EMAIL>"
}
```

**Response (200)**:
```json
{
  "success": true,
  "exists": true
}
```

## 🎓 Learning Objectives

After completing US-01, you'll understand:
- ✅ Flask application structure and blueprints
- ✅ SQLAlchemy ORM for database operations
- ✅ Password hashing and security best practices
- ✅ Frontend-backend API communication
- ✅ Form validation (client-side and server-side)
- ✅ Error handling and user feedback
- ✅ Testing Flask applications with pytest
- ✅ Database design and indexing
- ✅ Environment configuration management

## 🔄 Next Steps
After US-01 is complete and tested:
1. **US-02**: Login system with JWT tokens
2. **US-03**: Resume upload and parsing
3. **US-04**: Job description management
4. **US-05**: Resume-job matching algorithm

## 🚨 Important Notes
- Replace `.env.example` values with your actual database credentials
- Use a strong `SECRET_KEY` in production
- Test all functionality before proceeding to US-02
- The login page is a placeholder - full functionality comes in US-02
