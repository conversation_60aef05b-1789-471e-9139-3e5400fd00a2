# Dr. Resume US-01 Environment Configuration
# Copy this file to .env and update with your actual values

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost/dr_resume_dev

# Flask Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
FLASK_ENV=development
FLASK_DEBUG=True

# CORS Configuration (for frontend-backend communication)
CORS_ORIGINS=http://localhost:5000,http://127.0.0.1:5000

# Security Settings
MIN_PASSWORD_LENGTH=8

# Email Configuration (for future use)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Application Settings
APP_NAME=Dr. Resume
APP_VERSION=1.0.0
APP_DESCRIPTION=AI-Powered Resume Scanner & Job Matching Platform
