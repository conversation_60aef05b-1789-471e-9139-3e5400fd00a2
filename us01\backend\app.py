from flask import Flask, render_template, send_from_directory
from flask_cors import CORS
from models import db
from routes.us01_auth_routes import auth_bp
from config import Config
import os

def create_app():
    """Application factory pattern"""
    app = Flask(__name__, 
                template_folder='../frontend',
                static_folder='../frontend/static')
    
    # Load configuration
    app.config.from_object(Config)
    
    # Initialize extensions
    db.init_app(app)
    CORS(app, origins=app.config['CORS_ORIGINS'])
    
    # Register blueprints
    app.register_blueprint(auth_bp)
    
    # Frontend routes
    @app.route('/')
    def landing():
        """Serve the landing page"""
        return render_template('us01_landing.html')
    
    @app.route('/register')
    def register_page():
        """Serve the registration page"""
        return render_template('us01_register.html')
    
    @app.route('/login')
    def login_page():
        """Serve the login page (placeholder for US-02)"""
        return render_template('us01_login.html')
    
    # Health check endpoint
    @app.route('/health')
    def health_check():
        return {'status': 'healthy', 'service': 'Dr. Resume US-01'}, 200
    
    # Create database tables
    with app.app_context():
        db.create_all()
        print("Database tables created successfully!")
    
    return app

if __name__ == '__main__':
    app = create_app()
    print("🚀 Dr. Resume US-01 Server Starting...")
    print("📱 Landing Page: http://localhost:5000")
    print("📝 Register Page: http://localhost:5000/register")
    print("🔐 Login Page: http://localhost:5000/login")
    print("💚 Health Check: http://localhost:5000/health")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
