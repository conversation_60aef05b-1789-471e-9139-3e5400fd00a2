<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Dr. Resume</title>
    <link rel="stylesheet" href="/static/css/us01_styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                🔐 Login to Dr. Resume
            </h1>
        </div>
        
        <div class="content">
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-control" 
                        placeholder="<EMAIL>"
                        required
                    >
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="password-field">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-control" 
                            placeholder="••••••••"
                            required
                        >
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    Login
                </button>
            </form>
            
            <div class="footer-link">
                Don't have an account? <a href="/register">Register here</a>
            </div>
        </div>
    </div>

    <script>
        // Placeholder for US-02 login functionality
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Login functionality will be implemented in US-02!');
        });
    </script>
</body>
</html>
