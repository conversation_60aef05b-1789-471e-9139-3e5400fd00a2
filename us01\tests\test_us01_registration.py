import pytest
import json
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app import create_app
from models import db, User

@pytest.fixture
def app():
    """Create and configure a test app"""
    app = create_app()
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'  # Use in-memory SQLite for tests
    
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()

@pytest.fixture
def client(app):
    """Create a test client"""
    return app.test_client()

@pytest.fixture
def runner(app):
    """Create a test runner"""
    return app.test_cli_runner()

class TestUserRegistration:
    """Test cases for user registration functionality"""
    
    def test_successful_registration(self, client):
        """Test successful user registration"""
        data = {
            'first_name': '<PERSON>',
            'last_name': '<PERSON><PERSON>',
            'email': '<EMAIL>',
            'password': 'securepassword123',
            'confirm_password': 'securepassword123'
        }
        
        response = client.post('/api/register', 
                             data=json.dumps(data),
                             content_type='application/json')
        
        assert response.status_code == 201
        result = json.loads(response.data)
        assert result['success'] is True
        assert 'Account created successfully' in result['message']
        assert 'user' in result
        assert result['user']['email'] == '<EMAIL>'
    
    def test_duplicate_email_registration(self, client):
        """Test registration with duplicate email"""
        # First registration
        data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'password': 'securepassword123',
            'confirm_password': 'securepassword123'
        }
        
        client.post('/api/register', 
                   data=json.dumps(data),
                   content_type='application/json')
        
        # Second registration with same email
        data['first_name'] = 'Jane'
        response = client.post('/api/register', 
                             data=json.dumps(data),
                             content_type='application/json')
        
        assert response.status_code == 400
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'Email already registered' in result['errors']
    
    def test_invalid_email_format(self, client):
        """Test registration with invalid email format"""
        data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'email': 'invalid-email',
            'password': 'securepassword123',
            'confirm_password': 'securepassword123'
        }
        
        response = client.post('/api/register', 
                             data=json.dumps(data),
                             content_type='application/json')
        
        assert response.status_code == 400
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'Invalid email format' in result['errors']
    
    def test_password_mismatch(self, client):
        """Test registration with password mismatch"""
        data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'password': 'securepassword123',
            'confirm_password': 'differentpassword'
        }
        
        response = client.post('/api/register', 
                             data=json.dumps(data),
                             content_type='application/json')
        
        assert response.status_code == 400
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'Passwords do not match' in result['errors']
    
    def test_short_password(self, client):
        """Test registration with short password"""
        data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'password': '123',
            'confirm_password': '123'
        }
        
        response = client.post('/api/register', 
                             data=json.dumps(data),
                             content_type='application/json')
        
        assert response.status_code == 400
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'Password must be at least 8 characters long' in result['errors']
    
    def test_missing_required_fields(self, client):
        """Test registration with missing required fields"""
        data = {
            'first_name': '',
            'last_name': '',
            'email': '',
            'password': '',
            'confirm_password': ''
        }
        
        response = client.post('/api/register', 
                             data=json.dumps(data),
                             content_type='application/json')
        
        assert response.status_code == 400
        result = json.loads(response.data)
        assert result['success'] is False
        assert len(result['errors']) >= 4  # All required fields missing
    
    def test_check_email_exists(self, client):
        """Test email existence check endpoint"""
        # Register a user first
        data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'password': 'securepassword123',
            'confirm_password': 'securepassword123'
        }
        
        client.post('/api/register', 
                   data=json.dumps(data),
                   content_type='application/json')
        
        # Check if email exists
        response = client.post('/api/check-email',
                             data=json.dumps({'email': '<EMAIL>'}),
                             content_type='application/json')
        
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result['success'] is True
        assert result['exists'] is True
        
        # Check non-existent email
        response = client.post('/api/check-email',
                             data=json.dumps({'email': '<EMAIL>'}),
                             content_type='application/json')
        
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result['success'] is True
        assert result['exists'] is False

class TestUserModel:
    """Test cases for User model"""
    
    def test_password_hashing(self, app):
        """Test password hashing functionality"""
        with app.app_context():
            user = User('John', 'Doe', '<EMAIL>', 'testpassword')
            
            # Password should be hashed
            assert user.password_hash != 'testpassword'
            assert user.check_password('testpassword') is True
            assert user.check_password('wrongpassword') is False
    
    def test_email_validation(self):
        """Test email validation"""
        assert User.validate_email('<EMAIL>') is True
        assert User.validate_email('invalid-email') is False
        assert User.validate_email('') is False
        assert User.validate_email('test@') is False
    
    def test_password_validation(self):
        """Test password validation"""
        valid, message = User.validate_password('validpassword123')
        assert valid is True
        
        valid, message = User.validate_password('short')
        assert valid is False
        assert 'at least 8 characters' in message
    
    def test_user_to_dict(self, app):
        """Test user serialization"""
        with app.app_context():
            user = User('John', 'Doe', '<EMAIL>', 'testpassword')
            db.session.add(user)
            db.session.commit()
            
            user_dict = user.to_dict()
            assert 'password_hash' not in user_dict
            assert user_dict['email'] == '<EMAIL>'
            assert user_dict['first_name'] == 'John'
            assert user_dict['last_name'] == 'Doe'

if __name__ == '__main__':
    pytest.main([__file__])
