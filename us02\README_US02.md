# US-02: Login + JWT Token System 🔐

## 🎯 What We Built
A complete login system with JWT token authentication that builds on US-01:
- **JWT Authentication**: Secure token-based login with Flask-JWT-Extended
- **Protected Routes**: Dashboard accessible only with valid tokens
- **Token Management**: Automatic token storage and validation in frontend
- **Session Handling**: Proper login/logout flow with token cleanup
- **Security**: Password verification, token expiration, and error handling

## 📁 Project Structure
```
us02/
├── backend/
│   ├── app.py                    # 🚀 Flask app with JWT configuration
│   ├── models.py                 # 📊 User model with JWT token methods
│   ├── routes/
│   │   └── us02_auth_routes.py   # 🔐 Login, logout, profile endpoints
│   ├── config.py                 # ⚙️ JWT configuration settings
│   ├── requirements.txt          # 📦 Dependencies with Flask-JWT-Extended
│   └── .env                      # 🔧 Environment variables
├── frontend/
│   ├── us02_landing.html         # 🏠 Landing page with login redirect
│   ├── us02_register.html        # 📝 Registration (same as US-01)
│   ├── us02_login.html           # 🔐 Login form with JWT handling
│   ├── us02_dashboard.html       # 📊 Protected dashboard page
│   └── static/
│       ├── css/
│       │   └── us02_styles.css   # 🎨 Styles with dashboard design
│       └── js/
│           ├── us02_register.js  # ⚡ Registration logic
│           ├── us02_login.js     # 🔑 Login with token management
│           └── us02_dashboard.js # 📊 Dashboard with auth protection
├── tests/
│   └── test_us02_login.py        # 🧪 Login and JWT tests
└── README_US02.md                # 📖 This documentation
```

## 🔧 Technology Stack
- **Backend**: Flask 2.3.3, Flask-JWT-Extended 4.5.3, SQLAlchemy
- **Frontend**: HTML5, CSS3, Vanilla JavaScript with localStorage
- **Security**: JWT tokens, password hashing, CORS protection
- **Testing**: pytest with JWT token testing
- **Database**: SQLite with user authentication tracking

## 🚀 Complete Authentication Flow

### 1. Registration → Login Flow
1. User registers via `/register` (US-01 functionality)
2. Redirected to `/login` page
3. User enters credentials
4. Backend validates and returns JWT tokens
5. Frontend saves tokens to localStorage
6. User redirected to protected `/dashboard`

### 2. JWT Token Management
1. **Login**: `POST /api/login` returns access + refresh tokens
2. **Storage**: Tokens saved in localStorage
3. **Authentication**: All protected routes require `Authorization: Bearer <token>`
4. **Validation**: Frontend checks token validity on page load
5. **Logout**: Tokens cleared from localStorage

### 3. Protected Dashboard Access
1. Dashboard checks for valid token on load
2. If no token → redirect to login
3. If invalid token → clear storage and redirect
4. If valid token → load user info and show dashboard

## 📊 Database Schema (Enhanced from US-01)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,        -- NEW: Account status
    last_login TIMESTAMP,                  -- NEW: Track login activity
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 Quick Setup & Testing

### 1. Install Dependencies
```bash
cd us02/backend
pip install -r requirements.txt
```

### 2. Run Application
```bash
python app.py
```

### 3. Test the Complete Flow
1. **Landing**: http://localhost:5000
2. **Register**: http://localhost:5000/register
3. **Login**: http://localhost:5000/login
4. **Dashboard**: http://localhost:5000/dashboard (protected)

### 4. Test Registration → Login Flow
1. Register a new account
2. Get redirected to login page
3. Login with same credentials
4. Get redirected to dashboard
5. See personalized welcome message

### 5. Run Tests
```bash
cd ../tests
pytest test_us02_login.py -v
```

## 🔒 Security Features

✅ **JWT Token Security**
- HS256 algorithm for token signing
- 24-hour access token expiration
- 30-day refresh token expiration
- Secure token storage in localStorage

✅ **Authentication Protection**
- Protected routes require valid JWT tokens
- Automatic token validation on page load
- Token cleanup on logout
- Proper error handling for expired/invalid tokens

✅ **Password Security**
- Werkzeug password hashing (from US-01)
- Secure password verification
- Account status tracking (active/inactive)

## 📝 API Endpoints

### POST /api/login
Login user and return JWT tokens
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Success Response (200)**:
```json
{
  "success": true,
  "message": "Login successful",
  "user": {
    "id": 1,
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "is_active": true,
    "last_login": "2025-07-25T10:30:00"
  },
  "tokens": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_in": 86400
  }
}
```

**Error Response (401)**:
```json
{
  "success": false,
  "message": "Invalid email or password"
}
```

### GET /api/profile (Protected)
Get current user profile
```bash
Authorization: Bearer <access_token>
```

**Success Response (200)**:
```json
{
  "success": true,
  "user": {
    "id": 1,
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "is_active": true,
    "last_login": "2025-07-25T10:30:00"
  }
}
```

### POST /api/logout (Protected)
Logout user (clear tokens)
```bash
Authorization: Bearer <access_token>
```

**Success Response (200)**:
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

## 🎨 UI Features Implemented

✅ **Login Page** - Exact match to your design
- Email and password fields
- Password visibility toggle
- Real-time validation
- JWT token handling
- Automatic redirect to dashboard

✅ **Dashboard Page** - New protected interface
- Personalized welcome message
- Navigation with logout button
- Placeholder cards for future features (US-03, US-04)
- Statistics section
- Quick actions section
- Responsive design

✅ **Authentication Flow**
- Automatic login detection
- Token validation on page load
- Proper logout with token cleanup
- Session management

## 🧪 Testing Coverage

✅ **Login Tests**
- Successful login with valid credentials
- Invalid email/password handling
- Missing credentials validation
- Inactive user account handling

✅ **JWT Token Tests**
- Token generation and structure
- Protected route access with valid tokens
- Unauthorized access without tokens
- Invalid token handling
- Logout functionality

✅ **User Model Tests**
- JWT token generation methods
- Last login timestamp updates
- User authentication methods

## 🔄 Frontend Token Management

✅ **localStorage Integration**
- `dr_resume_token`: Access token storage
- `dr_resume_refresh_token`: Refresh token storage
- `dr_resume_user`: User information cache

✅ **Automatic Authentication**
- Page load token validation
- Automatic redirects based on auth status
- Token cleanup on logout/expiration

✅ **Error Handling**
- Network error handling
- Token expiration detection
- Invalid token cleanup

## 🎓 Learning Objectives

After completing US-02, you'll understand:
- ✅ JWT token authentication implementation
- ✅ Protected route creation with Flask-JWT-Extended
- ✅ Frontend token management with localStorage
- ✅ Authentication flow design (login → dashboard)
- ✅ Security best practices for web authentication
- ✅ Session management and logout handling
- ✅ Testing authentication systems
- ✅ User experience design for auth flows

## 🔄 Integration with US-01

US-02 builds directly on US-01:
- ✅ Uses same User model with enhancements
- ✅ Registration flow redirects to login
- ✅ Login system authenticates registered users
- ✅ Dashboard provides protected user interface

## 🔄 Next Steps
After US-02 is complete and tested:
1. **US-03**: Resume upload and file processing
2. **US-04**: Job description management
3. **US-05**: Keyword parsing and extraction
4. **US-06**: Resume-job matching algorithm

## 🚨 Important Notes
- JWT tokens are stored in localStorage (consider httpOnly cookies for production)
- Token expiration is set to 24 hours (configurable)
- Dashboard is a placeholder - full functionality comes in later US
- All authentication is working and ready for integration with future features

## ✅ US-02 Status: COMPLETE
- ✅ JWT authentication system working
- ✅ Login/logout flow functional
- ✅ Protected dashboard accessible
- ✅ Token management implemented
- ✅ Tests passing
- ✅ Ready for US-03 integration
