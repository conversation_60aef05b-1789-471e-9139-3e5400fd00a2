/* Dr. Resume US-02 Styles - Login + JWT + Dashboard */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 100%;
    max-width: 500px;
}

.header {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
}

.header h1 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.header .subtitle {
    font-size: 16px;
    opacity: 0.9;
    font-weight: 400;
}

.content {
    padding: 40px 30px;
}

.welcome-text {
    text-align: center;
    color: #6b7280;
    margin-bottom: 30px;
    font-size: 16px;
    line-height: 1.5;
}

.btn {
    width: 100%;
    padding: 16px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    margin-bottom: 12px;
}

.btn-primary {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(124, 58, 237, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #7c3aed;
    border: 2px solid #7c3aed;
}

.btn-secondary:hover {
    background: #7c3aed;
    color: white;
    transform: translateY(-2px);
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #374151;
    font-weight: 500;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.2s ease;
    background: #f9fafb;
}

.form-control:focus {
    outline: none;
    border-color: #7c3aed;
    background: white;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
}

.password-field {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    font-size: 14px;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 24px;
}

.checkbox-group input[type="checkbox"] {
    margin-top: 4px;
    accent-color: #7c3aed;
}

.checkbox-group label {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.4;
    margin-bottom: 0;
}

.checkbox-group a {
    color: #7c3aed;
    text-decoration: none;
}

.checkbox-group a:hover {
    text-decoration: underline;
}

.footer-link {
    text-align: center;
    margin-top: 24px;
    color: #6b7280;
    font-size: 14px;
}

.footer-link a {
    color: #7c3aed;
    text-decoration: none;
    font-weight: 500;
}

.footer-link a:hover {
    text-decoration: underline;
}

/* Alert Messages */
.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
}

.alert-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.alert-warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fcd34d;
}

/* Loading State */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Dashboard Styles */
.dashboard-container {
    max-width: 1200px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.dashboard-header {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    color: white;
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-nav {
    display: flex;
    gap: 20px;
    align-items: center;
}

.nav-item {
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.2);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.dashboard-content {
    padding: 40px;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.dashboard-card {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    transition: all 0.2s ease;
}

.dashboard-card:hover {
    border-color: #7c3aed;
    transform: translateY(-2px);
}

.card-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.card-description {
    color: #6b7280;
    margin-bottom: 20px;
    line-height: 1.5;
}

.card-button {
    background: #7c3aed;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.card-button:hover {
    background: #6d28d9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .dashboard-nav {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .container, .dashboard-container {
        margin: 10px;
        max-width: none;
    }
    
    .header, .dashboard-content {
        padding: 30px 20px;
    }
    
    .content {
        padding: 30px 20px;
    }
    
    .header h1 {
        font-size: 24px;
    }
}
