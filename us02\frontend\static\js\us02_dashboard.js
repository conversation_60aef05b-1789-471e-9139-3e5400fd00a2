// Dr. Resume US-02 Dashboard JavaScript with JWT Protection

document.addEventListener('DOMContentLoaded', function() {
    console.log('🩺 Dr. Resume Dashboard Loaded (US-02)');
    
    // Check authentication
    checkAuthentication();
    
    // Load user info
    loadUserInfo();
});

async function checkAuthentication() {
    const token = localStorage.getItem('dr_resume_token');
    
    if (!token) {
        console.log('❌ No token found, redirecting to login');
        window.location.href = '/login';
        return;
    }
    
    try {
        // Verify token by calling protected endpoint
        const response = await fetch('/api/profile', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('Token verification failed');
        }
        
        const result = await response.json();
        
        if (!result.success) {
            throw new Error('Invalid token response');
        }
        
        console.log('✅ Token verified successfully');
        
        // Update user info in localStorage if needed
        localStorage.setItem('dr_resume_user', JSON.stringify(result.user));
        
    } catch (error) {
        console.error('Authentication error:', error);
        
        // Clear invalid tokens and redirect
        localStorage.removeItem('dr_resume_token');
        localStorage.removeItem('dr_resume_refresh_token');
        localStorage.removeItem('dr_resume_user');
        
        alert('Your session has expired. Please log in again.');
        window.location.href = '/login';
    }
}

function loadUserInfo() {
    try {
        const userStr = localStorage.getItem('dr_resume_user');
        
        if (userStr) {
            const user = JSON.parse(userStr);
            
            // Update welcome message
            const welcomeMessage = document.getElementById('welcomeMessage');
            const userName = document.getElementById('userName');
            
            if (welcomeMessage) {
                welcomeMessage.textContent = `Welcome, ${user.first_name}`;
            }
            
            if (userName) {
                userName.textContent = user.first_name;
            }
            
            // Update logout button
            const logoutBtn = document.querySelector('.logout-btn');
            if (logoutBtn) {
                logoutBtn.textContent = '🚪 Logout';
                logoutBtn.onclick = logout;
            }
            
            console.log('👤 User info loaded:', user.first_name, user.email);
        }
        
    } catch (error) {
        console.error('Error loading user info:', error);
    }
}

async function logout() {
    try {
        const token = localStorage.getItem('dr_resume_token');
        
        if (token) {
            // Call logout endpoint
            await fetch('/api/logout', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
        }
        
    } catch (error) {
        console.error('Logout API error:', error);
    } finally {
        // Clear all stored data
        localStorage.removeItem('dr_resume_token');
        localStorage.removeItem('dr_resume_refresh_token');
        localStorage.removeItem('dr_resume_user');
        
        console.log('🚪 Logged out successfully');
        
        // Redirect to landing page
        window.location.href = '/';
    }
}

// Placeholder functions for future US implementations
function addJobDescription() {
    alert('📄 Job Description functionality will be implemented in US-04!');
    console.log('🚧 US-04: Job Description Management - Coming Soon');
}

function uploadResume() {
    alert('📤 Resume Upload functionality will be implemented in US-03!');
    console.log('🚧 US-03: Resume Upload - Coming Soon');
}

function runAnalysis() {
    alert('🔍 Resume Analysis functionality will be implemented in US-06!');
    console.log('🚧 US-06: Resume-Job Matching - Coming Soon');
}

// Utility function to make authenticated API calls
async function makeAuthenticatedRequest(url, options = {}) {
    const token = localStorage.getItem('dr_resume_token');
    
    if (!token) {
        throw new Error('No authentication token found');
    }
    
    const defaultOptions = {
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            ...options.headers
        }
    };
    
    const mergedOptions = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, mergedOptions);
        
        if (response.status === 401) {
            // Token expired or invalid
            localStorage.removeItem('dr_resume_token');
            localStorage.removeItem('dr_resume_refresh_token');
            localStorage.removeItem('dr_resume_user');
            window.location.href = '/login';
            return;
        }
        
        return response;
        
    } catch (error) {
        console.error('Authenticated request error:', error);
        throw error;
    }
}

// Export functions for use in other scripts
window.DrResumeAuth = {
    logout,
    makeAuthenticatedRequest,
    checkAuthentication
};
