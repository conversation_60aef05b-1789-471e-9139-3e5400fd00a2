// Dr. Resume US-02 Login JavaScript with JWT Token Management

document.addEventListener('DOMContentLoaded', function() {
    console.log('🩺 Dr. Resume Login Page Loaded (US-02)');
    
    // Check if user is already logged in
    const token = localStorage.getItem('dr_resume_token');
    if (token) {
        // Verify token is still valid
        verifyTokenAndRedirect();
        return;
    }
    
    const form = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const alertContainer = document.getElementById('alertContainer');
    
    // Form submission handler
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Clear previous alerts
        clearAlerts();
        
        // Get form data
        const formData = new FormData(form);
        const data = {
            email: formData.get('email').trim(),
            password: formData.get('password')
        };
        
        // Client-side validation
        const validationErrors = validateForm(data);
        if (validationErrors.length > 0) {
            showAlert('error', 'Please fix the following errors:', validationErrors);
            return;
        }
        
        // Show loading state
        setLoading(true);
        
        try {
            // Send login request
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Success - save token and redirect
                showAlert('success', result.message);
                
                // Save JWT token to localStorage
                localStorage.setItem('dr_resume_token', result.tokens.access_token);
                localStorage.setItem('dr_resume_refresh_token', result.tokens.refresh_token);
                
                // Save user info
                localStorage.setItem('dr_resume_user', JSON.stringify(result.user));
                
                console.log('✅ Login successful, token saved');
                console.log('🔑 Token:', result.tokens.access_token.substring(0, 20) + '...');
                
                // Redirect to dashboard after 1 second
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1000);
                
            } else {
                // Show error message
                showAlert('error', result.message);
            }
            
        } catch (error) {
            console.error('Login error:', error);
            showAlert('error', 'Network error. Please check your connection and try again.');
        } finally {
            setLoading(false);
        }
    });
});

async function verifyTokenAndRedirect() {
    try {
        const token = localStorage.getItem('dr_resume_token');
        
        // Test token by calling protected endpoint
        const response = await fetch('/api/profile', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            // Token is valid, redirect to dashboard
            console.log('✅ Valid token found, redirecting to dashboard');
            window.location.href = '/dashboard';
        } else {
            // Token is invalid, clear it
            console.log('❌ Invalid token, clearing storage');
            localStorage.removeItem('dr_resume_token');
            localStorage.removeItem('dr_resume_refresh_token');
            localStorage.removeItem('dr_resume_user');
        }
        
    } catch (error) {
        console.error('Token verification error:', error);
        // Clear invalid tokens
        localStorage.removeItem('dr_resume_token');
        localStorage.removeItem('dr_resume_refresh_token');
        localStorage.removeItem('dr_resume_user');
    }
}

// Validation functions
function validateForm(data) {
    const errors = [];
    
    // Required fields
    if (!data.email) errors.push('Email is required');
    if (!data.password) errors.push('Password is required');
    
    // Email validation
    if (data.email && !isValidEmail(data.email)) {
        errors.push('Please enter a valid email address');
    }
    
    return errors;
}

function isValidEmail(email) {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
}

// UI Helper functions
function showAlert(type, message, errors = []) {
    const alertContainer = document.getElementById('alertContainer');
    
    let alertHTML = `<div class="alert alert-${type}">`;
    alertHTML += `<strong>${message}</strong>`;
    
    if (errors.length > 0) {
        alertHTML += '<ul style="margin: 8px 0 0 20px;">';
        errors.forEach(error => {
            alertHTML += `<li>${error}</li>`;
        });
        alertHTML += '</ul>';
    }
    
    alertHTML += '</div>';
    
    alertContainer.innerHTML = alertHTML;
    
    // Scroll to top to show alert
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function clearAlerts() {
    document.getElementById('alertContainer').innerHTML = '';
}

function setLoading(loading) {
    const loginBtn = document.getElementById('loginBtn');
    
    if (loading) {
        loginBtn.disabled = true;
        loginBtn.classList.add('loading');
        loginBtn.textContent = 'Signing In...';
    } else {
        loginBtn.disabled = false;
        loginBtn.classList.remove('loading');
        loginBtn.textContent = 'Login';
    }
}

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    
    if (field.type === 'password') {
        field.type = 'text';
        button.textContent = '🙈';
    } else {
        field.type = 'password';
        button.textContent = '👁️';
    }
}
