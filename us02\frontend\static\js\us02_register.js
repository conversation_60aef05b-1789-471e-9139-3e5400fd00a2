// Dr. Resume US-02 Registration JavaScript (same as US-01 but with redirect to login)

document.addEventListener('DOMContentLoaded', function() {
    console.log('🩺 Dr. Resume Registration Page Loaded (US-02)');
    
    const form = document.getElementById('registerForm');
    const submitBtn = document.getElementById('submitBtn');
    const alertContainer = document.getElementById('alertContainer');
    
    // Form submission handler
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Clear previous alerts
        clearAlerts();
        
        // Get form data
        const formData = new FormData(form);
        const data = {
            first_name: formData.get('first_name').trim(),
            last_name: formData.get('last_name').trim(),
            email: formData.get('email').trim(),
            password: formData.get('password'),
            confirm_password: formData.get('confirm_password')
        };
        
        // Client-side validation
        const validationErrors = validateForm(data);
        if (validationErrors.length > 0) {
            showAlert('error', 'Please fix the following errors:', validationErrors);
            return;
        }
        
        // Show loading state
        setLoading(true);
        
        try {
            // Send registration request
            const response = await fetch('/api/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Success - show message and redirect
                showAlert('success', result.message);
                
                // Clear form
                form.reset();
                
                // Redirect to login page after 2 seconds
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
                
            } else {
                // Show validation errors
                if (result.errors && result.errors.length > 0) {
                    showAlert('error', result.message, result.errors);
                } else {
                    showAlert('error', result.message);
                }
            }
            
        } catch (error) {
            console.error('Registration error:', error);
            showAlert('error', 'Network error. Please check your connection and try again.');
        } finally {
            setLoading(false);
        }
    });
    
    // Real-time email validation
    const emailInput = document.getElementById('email');
    emailInput.addEventListener('blur', async function() {
        const email = this.value.trim();
        if (email && isValidEmail(email)) {
            await checkEmailExists(email);
        }
    });
    
    // Real-time password confirmation
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    
    confirmPasswordInput.addEventListener('input', function() {
        const password = passwordInput.value;
        const confirmPassword = this.value;
        
        if (confirmPassword && password !== confirmPassword) {
            this.style.borderColor = '#ef4444';
        } else {
            this.style.borderColor = '#e5e7eb';
        }
    });
});

// Validation functions
function validateForm(data) {
    const errors = [];
    
    // Required fields
    if (!data.first_name) errors.push('First name is required');
    if (!data.last_name) errors.push('Last name is required');
    if (!data.email) errors.push('Email is required');
    if (!data.password) errors.push('Password is required');
    if (!data.confirm_password) errors.push('Password confirmation is required');
    
    // Email validation
    if (data.email && !isValidEmail(data.email)) {
        errors.push('Please enter a valid email address');
    }
    
    // Password validation
    if (data.password && data.password.length < 8) {
        errors.push('Password must be at least 8 characters long');
    }
    
    // Password confirmation
    if (data.password && data.confirm_password && data.password !== data.confirm_password) {
        errors.push('Passwords do not match');
    }
    
    // Terms agreement
    const agreeTerms = document.getElementById('agreeTerms').checked;
    if (!agreeTerms) {
        errors.push('You must agree to the Terms of Service and Privacy Policy');
    }
    
    return errors;
}

function isValidEmail(email) {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
}

async function checkEmailExists(email) {
    try {
        const response = await fetch('/api/check-email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email })
        });
        
        const result = await response.json();
        
        if (result.success && result.exists) {
            showAlert('warning', 'This email is already registered. Please use a different email or sign in.');
        }
        
    } catch (error) {
        console.error('Email check error:', error);
    }
}

// UI Helper functions
function showAlert(type, message, errors = []) {
    const alertContainer = document.getElementById('alertContainer');
    
    let alertHTML = `<div class="alert alert-${type}">`;
    alertHTML += `<strong>${message}</strong>`;
    
    if (errors.length > 0) {
        alertHTML += '<ul style="margin: 8px 0 0 20px;">';
        errors.forEach(error => {
            alertHTML += `<li>${error}</li>`;
        });
        alertHTML += '</ul>';
    }
    
    alertHTML += '</div>';
    
    alertContainer.innerHTML = alertHTML;
    
    // Scroll to top to show alert
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function clearAlerts() {
    document.getElementById('alertContainer').innerHTML = '';
}

function setLoading(loading) {
    const submitBtn = document.getElementById('submitBtn');
    
    if (loading) {
        submitBtn.disabled = true;
        submitBtn.classList.add('loading');
        submitBtn.textContent = 'Creating Account...';
    } else {
        submitBtn.disabled = false;
        submitBtn.classList.remove('loading');
        submitBtn.textContent = 'Create Account';
    }
}

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    
    if (field.type === 'password') {
        field.type = 'text';
        button.textContent = '🙈';
    } else {
        field.type = 'password';
        button.textContent = '👁️';
    }
}
