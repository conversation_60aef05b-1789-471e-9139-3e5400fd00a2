<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Dr. Resume</title>
    <link rel="stylesheet" href="/static/css/us02_styles.css">
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <div>
                <h1>🩺 Dr. Resume</h1>
                <nav class="dashboard-nav">
                    <a href="#" class="nav-item active">📊 Dashboard</a>
                    <a href="#" class="nav-item">📄 API Docs</a>
                    <a href="#" class="nav-item">🏠 Home</a>
                </nav>
            </div>
            
            <div class="user-info">
                <span id="welcomeMessage">Welcome, GUEST</span>
                <button class="logout-btn" onclick="logout()">🔐 Login</button>
            </div>
        </div>
        
        <div class="dashboard-content">
            <h2 style="margin-bottom: 30px; color: #1f2937;">Welcome, <span id="userName">GUEST</span></h2>
            <p style="color: #6b7280; margin-bottom: 40px;">AI-Powered Resume Scanner & Job Matching Platform</p>
            
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-icon">📄</div>
                    <h3 class="card-title">Job Descriptions</h3>
                    <p class="card-description">No job descriptions yet. Add one to get started!</p>
                    <button class="card-button" onclick="addJobDescription()">
                        ➕ Add Job Description
                    </button>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-icon">📋</div>
                    <h3 class="card-title">Resumes</h3>
                    <p class="card-description">No resumes uploaded yet. Upload one to get started!</p>
                    <button class="card-button" onclick="uploadResume()">
                        📤 Upload Resume
                    </button>
                </div>
            </div>
            
            <!-- Stats Section -->
            <div style="background: #f8fafc; border-radius: 12px; padding: 30px; margin-top: 30px;">
                <h3 style="color: #1f2937; margin-bottom: 20px;">📊 Dashboard</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <div style="text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; color: #7c3aed;">0</div>
                        <div style="color: #6b7280;">Resumes</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; color: #7c3aed;">1</div>
                        <div style="color: #6b7280;">Job Descriptions</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; color: #7c3aed;">0</div>
                        <div style="color: #6b7280;">Scans</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; color: #7c3aed;">0%</div>
                        <div style="color: #6b7280;">Avg. Match</div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Scans Section -->
            <div style="margin-top: 30px;">
                <h3 style="color: #1f2937; margin-bottom: 20px;">🔍 Recent Scans</h3>
                <div style="background: #f8fafc; border-radius: 12px; padding: 30px; text-align: center; color: #6b7280;">
                    No recent scans found.
                </div>
            </div>
            
            <!-- Quick Actions Section -->
            <div style="margin-top: 30px;">
                <h3 style="color: #1f2937; margin-bottom: 20px;">⚡ Quick Actions</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <button class="card-button" onclick="uploadResume()" style="padding: 16px;">
                        📤 Upload Resume
                    </button>
                    <button class="card-button" onclick="addJobDescription()" style="padding: 16px;">
                        ➕ Add Job Description
                    </button>
                    <button class="card-button" onclick="runAnalysis()" style="padding: 16px;">
                        🔍 Run Analysis
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/us02_dashboard.js"></script>
</body>
</html>
