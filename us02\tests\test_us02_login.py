import pytest
import json
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app import create_app
from models import db, User

@pytest.fixture
def app():
    """Create and configure a test app"""
    app = create_app()
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'  # Use in-memory SQLite for tests
    app.config['JWT_SECRET_KEY'] = 'test-jwt-secret'
    
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()

@pytest.fixture
def client(app):
    """Create a test client"""
    return app.test_client()

@pytest.fixture
def test_user(app):
    """Create a test user"""
    with app.app_context():
        user = User('Test', 'User', '<EMAIL>', 'testpassword123')
        db.session.add(user)
        db.session.commit()
        return user

class TestUserLogin:
    """Test cases for user login functionality"""
    
    def test_successful_login(self, client, test_user):
        """Test successful user login"""
        data = {
            'email': '<EMAIL>',
            'password': 'testpassword123'
        }
        
        response = client.post('/api/login', 
                             data=json.dumps(data),
                             content_type='application/json')
        
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result['success'] is True
        assert 'Login successful' in result['message']
        assert 'tokens' in result
        assert 'access_token' in result['tokens']
        assert 'refresh_token' in result['tokens']
        assert result['user']['email'] == '<EMAIL>'
    
    def test_invalid_email_login(self, client, test_user):
        """Test login with invalid email"""
        data = {
            'email': '<EMAIL>',
            'password': 'testpassword123'
        }
        
        response = client.post('/api/login', 
                             data=json.dumps(data),
                             content_type='application/json')
        
        assert response.status_code == 401
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'Invalid email or password' in result['message']
    
    def test_invalid_password_login(self, client, test_user):
        """Test login with invalid password"""
        data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        
        response = client.post('/api/login', 
                             data=json.dumps(data),
                             content_type='application/json')
        
        assert response.status_code == 401
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'Invalid email or password' in result['message']
    
    def test_missing_credentials(self, client):
        """Test login with missing credentials"""
        data = {
            'email': '',
            'password': ''
        }
        
        response = client.post('/api/login', 
                             data=json.dumps(data),
                             content_type='application/json')
        
        assert response.status_code == 400
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'errors' in result
        assert 'Email is required' in result['errors']
        assert 'Password is required' in result['errors']
    
    def test_inactive_user_login(self, client, app):
        """Test login with inactive user"""
        with app.app_context():
            # Create inactive user
            user = User('Inactive', 'User', '<EMAIL>', 'testpassword123')
            user.is_active = False
            db.session.add(user)
            db.session.commit()
        
        data = {
            'email': '<EMAIL>',
            'password': 'testpassword123'
        }
        
        response = client.post('/api/login', 
                             data=json.dumps(data),
                             content_type='application/json')
        
        assert response.status_code == 401
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'Account is deactivated' in result['message']

class TestJWTProtectedRoutes:
    """Test cases for JWT protected routes"""
    
    def test_profile_with_valid_token(self, client, test_user):
        """Test accessing profile with valid JWT token"""
        # First login to get token
        login_data = {
            'email': '<EMAIL>',
            'password': 'testpassword123'
        }
        
        login_response = client.post('/api/login', 
                                   data=json.dumps(login_data),
                                   content_type='application/json')
        
        login_result = json.loads(login_response.data)
        token = login_result['tokens']['access_token']
        
        # Access profile with token
        response = client.get('/api/profile',
                            headers={'Authorization': f'Bearer {token}'})
        
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result['success'] is True
        assert result['user']['email'] == '<EMAIL>'
    
    def test_profile_without_token(self, client):
        """Test accessing profile without token"""
        response = client.get('/api/profile')
        
        assert response.status_code == 401
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'authorization_required' in result['error']
    
    def test_profile_with_invalid_token(self, client):
        """Test accessing profile with invalid token"""
        response = client.get('/api/profile',
                            headers={'Authorization': 'Bearer invalid-token'})
        
        assert response.status_code == 401
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'invalid_token' in result['error']
    
    def test_logout_with_valid_token(self, client, test_user):
        """Test logout with valid token"""
        # First login to get token
        login_data = {
            'email': '<EMAIL>',
            'password': 'testpassword123'
        }
        
        login_response = client.post('/api/login', 
                                   data=json.dumps(login_data),
                                   content_type='application/json')
        
        login_result = json.loads(login_response.data)
        token = login_result['tokens']['access_token']
        
        # Logout with token
        response = client.post('/api/logout',
                             headers={'Authorization': f'Bearer {token}'})
        
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result['success'] is True
        assert 'Logged out successfully' in result['message']

class TestUserModel:
    """Test cases for User model JWT functionality"""
    
    def test_generate_tokens(self, app):
        """Test JWT token generation"""
        with app.app_context():
            user = User('John', 'Doe', '<EMAIL>', 'testpassword')
            db.session.add(user)
            db.session.commit()
            
            tokens = user.generate_tokens()
            
            assert 'access_token' in tokens
            assert 'refresh_token' in tokens
            assert 'token_type' in tokens
            assert 'expires_in' in tokens
            assert tokens['token_type'] == 'Bearer'
            assert tokens['expires_in'] == 86400  # 24 hours
    
    def test_update_last_login(self, app):
        """Test last login update"""
        with app.app_context():
            user = User('John', 'Doe', '<EMAIL>', 'testpassword')
            db.session.add(user)
            db.session.commit()
            
            # Initially no last login
            assert user.last_login is None
            
            # Update last login
            user.update_last_login()
            
            # Should now have last login timestamp
            assert user.last_login is not None

if __name__ == '__main__':
    pytest.main([__file__])
