# US-03: Resume Upload System 📤

## 🎯 What We Built
A complete resume upload and parsing system that builds on US-01 & US-02:
- **File Upload**: Secure upload of PDF, DOC, DOCX files with drag-drop support
- **File Parsing**: Extract text content from uploaded resumes using PyPDF2 and python-docx
- **File Storage**: Save files to local filesystem with organized structure
- **Database Integration**: Store file metadata, paths, and extracted text
- **Resume Management**: View, delete, and manage uploaded resumes
- **Security**: JWT-protected endpoints with file validation and size limits

## 📁 Project Structure
```
us03/
├── backend/
│   ├── app.py                    # 🚀 Flask app with upload endpoints
│   ├── models.py                 # 📊 User + Resume models
│   ├── config.py                 # ⚙️ Upload configuration
│   ├── routes/
│   │   ├── us03_auth_routes.py   # 🔐 Authentication (from US-02)
│   │   └── us03_upload_routes.py # 📤 Resume upload endpoints
│   ├── services/
│   │   └── file_parser.py        # 📄 PDF/DOC text extraction
│   ├── uploads/
│   │   └── resumes/              # 📁 Uploaded resume files
│   ├── requirements.txt          # 📦 Dependencies with PyPDF2, python-docx
│   └── .env                      # 🔧 Environment variables
├── frontend/
│   ├── us03_landing.html         # 🏠 Landing page
│   ├── us03_register.html        # 📝 Registration page
│   ├── us03_login.html           # 🔐 Login page
│   ├── us03_dashboard.html       # 📊 Dashboard with resume list
│   ├── us03_upload.html          # 📤 Resume upload page
│   └── static/
│       ├── css/
│       │   └── us03_styles.css   # 🎨 Styles with upload UI
│       └── js/
│           ├── us03_register.js  # ⚡ Registration logic
│           ├── us03_login.js     # 🔑 Login logic
│           ├── us03_dashboard.js # 📊 Dashboard with resume management
│           └── us03_upload.js    # 📤 Upload with drag-drop
├── tests/
│   └── test_us03_upload.py       # 🧪 Upload and parsing tests
└── README_US03.md                # 📖 This documentation
```

## 🔧 Technology Stack
- **Backend**: Flask 2.3.3, Flask-JWT-Extended, SQLAlchemy
- **File Processing**: PyPDF2 3.0.1, python-docx 0.8.11
- **Frontend**: HTML5, CSS3, Vanilla JavaScript with drag-drop
- **Security**: JWT tokens, file validation, size limits (16MB)
- **Testing**: pytest with file upload testing
- **Database**: SQLite with resume metadata and extracted text

## 🚀 Complete Upload Flow

### 1. Authentication → Upload Flow
1. User logs in (US-02 functionality)
2. Accesses dashboard with "Upload Resume" button
3. Navigates to `/upload` page
4. Drags/drops or selects PDF/DOC/DOCX file
5. Optionally adds resume title
6. Uploads file with progress tracking
7. Backend saves file and extracts text
8. User redirected to dashboard with updated resume list

### 2. File Processing Pipeline
1. **Validation**: Check file type (PDF/DOC/DOCX) and size (max 16MB)
2. **Storage**: Save to `/uploads/resumes/` with unique filename
3. **Database**: Create Resume record with metadata
4. **Parsing**: Extract text using PyPDF2 or python-docx
5. **Update**: Store extracted text and update status

### 3. Resume Management
1. **Dashboard**: View all uploaded resumes with metadata
2. **Details**: Click to view extracted text content
3. **Delete**: Remove resume file and database record
4. **Re-parse**: Re-extract text if needed

## 📊 Database Schema (Enhanced from US-02)
```sql
-- Users table (from US-02)
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- NEW: Resumes table
CREATE TABLE resumes (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER NOT NULL,
    file_type VARCHAR(10) NOT NULL,
    extracted_text TEXT,
    title VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    upload_status VARCHAR(20) DEFAULT 'processing',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 Quick Setup & Testing

### 1. Install Dependencies
```bash
cd us03/backend
pip install -r requirements.txt
```

### 2. Run Application
```bash
python app.py
```

### 3. Test the Complete Flow
1. **Register/Login**: Create account and login
2. **Dashboard**: http://localhost:5000/dashboard
3. **Upload**: Click "Upload Resume" → http://localhost:5000/upload
4. **Test Upload**: Drag/drop a PDF or DOC file
5. **View Results**: Return to dashboard to see uploaded resume
6. **Manage**: View details, delete resumes

### 4. Test File Types
- **PDF**: Upload any PDF file to test text extraction
- **DOCX**: Upload Word documents (2007+)
- **DOC**: Legacy format (shows error message)
- **Invalid**: Try .txt file to test validation

### 5. Run Tests
```bash
cd ../tests
pytest test_us03_upload.py -v
```

## 📝 API Endpoints

### POST /api/upload_resume (Protected)
Upload and parse resume file
```bash
Authorization: Bearer <access_token>
Content-Type: multipart/form-data

Form Data:
- resume: File (PDF/DOC/DOCX)
- title: String (optional)
```

**Success Response (201)**:
```json
{
  "success": true,
  "message": "Resume uploaded and processed successfully",
  "resume": {
    "id": 1,
    "original_filename": "john_doe_resume.pdf",
    "file_size": 245760,
    "file_size_formatted": "240.0 KB",
    "file_type": "pdf",
    "title": "Software Engineer Resume",
    "upload_status": "completed",
    "has_extracted_text": true,
    "text_length": 1250,
    "created_at": "2025-07-25T10:30:00"
  },
  "parsing_success": true
}
```

### GET /api/resumes (Protected)
Get all user's resumes
```bash
Authorization: Bearer <access_token>
```

**Success Response (200)**:
```json
{
  "success": true,
  "resumes": [
    {
      "id": 1,
      "original_filename": "resume.pdf",
      "file_size_formatted": "240.0 KB",
      "file_type": "pdf",
      "title": "My Resume",
      "upload_status": "completed",
      "created_at": "2025-07-25T10:30:00"
    }
  ],
  "count": 1
}
```

### GET /api/resumes/{id} (Protected)
Get specific resume details with extracted text
```bash
Authorization: Bearer <access_token>
```

### DELETE /api/resumes/{id} (Protected)
Delete resume file and database record
```bash
Authorization: Bearer <access_token>
```

## 🎨 UI Features Implemented

✅ **Upload Page** - Professional drag-drop interface
- Drag and drop file upload area
- File type validation (PDF, DOC, DOCX)
- File size display and validation (16MB limit)
- Optional resume title input
- Upload progress bar with status
- Success/error messaging

✅ **Enhanced Dashboard** - Resume management interface
- Resume list with metadata (filename, size, type, date)
- Upload status indicators (Processing, Completed, Failed)
- View resume details modal with extracted text
- Delete resume functionality
- Updated statistics (resume count)
- Quick action buttons

✅ **File Management**
- Secure file storage in organized directories
- Unique filename generation to prevent conflicts
- File cleanup on deletion
- Error handling for corrupted files

## 🔒 Security Features

✅ **File Upload Security**
- JWT authentication required for all upload endpoints
- File type validation (whitelist: PDF, DOC, DOCX)
- File size limits (16MB maximum)
- Secure filename generation with UUIDs
- File content validation during parsing

✅ **Data Protection**
- User isolation (users can only access their own resumes)
- Secure file paths outside web root
- Input sanitization and validation
- Error handling without sensitive data exposure

## 🧪 Testing Coverage

✅ **Upload Tests**
- Successful PDF upload and parsing
- Authentication requirement enforcement
- Invalid file type rejection
- File size limit validation
- Missing file handling

✅ **Resume Management Tests**
- Fetching user's resumes
- Getting specific resume details
- Deleting resumes
- User isolation verification

✅ **File Parser Tests**
- PDF text extraction
- DOCX text extraction
- File type validation
- Text cleaning and formatting

✅ **Model Tests**
- Resume creation and validation
- File size formatting
- Resume serialization
- Database relationships

## 🔄 File Processing Details

✅ **PDF Processing** (PyPDF2)
- Multi-page text extraction
- Encrypted PDF detection
- Error handling for corrupted files
- Text cleaning and formatting

✅ **DOCX Processing** (python-docx)
- Paragraph text extraction
- Table content extraction
- Formatting preservation
- Error handling for invalid files

✅ **DOC Processing** (Legacy)
- Currently shows error message
- Suggests conversion to DOCX/PDF
- Ready for future antiword integration

## 🎓 Learning Objectives

After completing US-03, you'll understand:
- ✅ File upload implementation with Flask
- ✅ Multipart form data handling
- ✅ File parsing with PyPDF2 and python-docx
- ✅ Secure file storage and organization
- ✅ Database relationships (User → Resume)
- ✅ Drag-and-drop UI implementation
- ✅ Progress tracking and user feedback
- ✅ File validation and security
- ✅ Error handling in file operations
- ✅ Testing file upload functionality

## 🔄 Integration with Previous US

US-03 builds on US-01 & US-02:
- ✅ Uses authentication system from US-02
- ✅ Extends User model with resume relationship
- ✅ Enhances dashboard with resume management
- ✅ Maintains consistent UI/UX design

## 🔄 Next Steps
After US-03 is complete and tested:
1. **US-04**: Job description upload and management
2. **US-05**: Keyword parsing and extraction
3. **US-06**: Resume-job matching algorithm
4. **US-07**: AI-powered suggestions

## 🚨 Important Notes
- Upload directory is created automatically on app start
- Files are stored with UUID filenames to prevent conflicts
- Extracted text is stored in database for fast access
- File parsing happens synchronously (consider async for production)
- DOC format support requires additional libraries (antiword/docx2txt)

## ✅ US-03 Status: COMPLETE
- ✅ Resume upload system working
- ✅ File parsing (PDF/DOCX) functional
- ✅ Resume management implemented
- ✅ Dashboard enhanced with resume list
- ✅ Tests passing
- ✅ Ready for US-04 integration
