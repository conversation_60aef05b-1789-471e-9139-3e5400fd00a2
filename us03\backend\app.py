from flask import Flask, render_template, send_from_directory
from flask_cors import CORS
from flask_jwt_extended import <PERSON><PERSON><PERSON><PERSON><PERSON>
from models import db
from routes.us03_auth_routes import auth_bp
from routes.us03_upload_routes import upload_bp
from config import Config
import os

def create_app():
    """Application factory pattern"""
    app = Flask(__name__, 
                template_folder='../frontend',
                static_folder='../frontend/static')
    
    # Load configuration
    app.config.from_object(Config)
    
    # Initialize upload directories
    Config.init_app(app)
    
    # Initialize extensions
    db.init_app(app)
    CORS(app, origins=app.config['CORS_ORIGINS'])
    
    # Initialize JWT
    jwt = JWTManager(app)
    
    # JWT error handlers
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return {
            'success': False,
            'message': 'Token has expired',
            'error': 'token_expired'
        }, 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return {
            'success': False,
            'message': 'Invalid token',
            'error': 'invalid_token'
        }, 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return {
            'success': False,
            'message': 'Authorization token is required',
            'error': 'authorization_required'
        }, 401
    
    # Register blueprints
    app.register_blueprint(auth_bp)
    app.register_blueprint(upload_bp)
    
    # Frontend routes
    @app.route('/')
    def landing():
        """Serve the landing page"""
        return render_template('us03_landing.html')
    
    @app.route('/register')
    def register_page():
        """Serve the registration page"""
        return render_template('us03_register.html')
    
    @app.route('/login')
    def login_page():
        """Serve the login page"""
        return render_template('us03_login.html')
    
    @app.route('/dashboard')
    def dashboard_page():
        """Serve the dashboard page (protected)"""
        return render_template('us03_dashboard.html')
    
    @app.route('/upload')
    def upload_page():
        """Serve the upload page (protected)"""
        return render_template('us03_upload.html')
    
    # Health check endpoint
    @app.route('/health')
    def health_check():
        return {'status': 'healthy', 'service': 'Dr. Resume US-03'}, 200
    
    # File upload error handler
    @app.errorhandler(413)
    def too_large(e):
        return jsonify({
            'success': False,
            'message': 'File too large. Maximum size is 16MB.',
            'error': 'file_too_large'
        }), 413
    
    # Create database tables
    with app.app_context():
        db.create_all()
        print("Database tables created successfully!")
    
    return app

if __name__ == '__main__':
    app = create_app()
    print("🚀 Dr. Resume US-03 Server Starting...")
    print("📱 Landing Page: http://localhost:5000")
    print("📝 Register Page: http://localhost:5000/register")
    print("🔐 Login Page: http://localhost:5000/login")
    print("📊 Dashboard: http://localhost:5000/dashboard")
    print("📤 Upload Page: http://localhost:5000/upload")
    print("💚 Health Check: http://localhost:5000/health")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
