import pytest
import json
import sys
import os
import tempfile
from io import Bytes<PERSON>

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app import create_app
from models import db, User, Resume

@pytest.fixture
def app():
    """Create and configure a test app"""
    app = create_app()
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    app.config['JWT_SECRET_KEY'] = 'test-jwt-secret'
    app.config['UPLOAD_FOLDER'] = tempfile.mkdtemp()
    app.config['RESUME_UPLOAD_FOLDER'] = os.path.join(app.config['UPLOAD_FOLDER'], 'resumes')
    
    # Create upload directory
    os.makedirs(app.config['RESUME_UPLOAD_FOLDER'], exist_ok=True)
    
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()

@pytest.fixture
def client(app):
    """Create a test client"""
    return app.test_client()

@pytest.fixture
def test_user(app):
    """Create a test user"""
    with app.app_context():
        user = User('Test', 'User', '<EMAIL>', 'testpassword123')
        db.session.add(user)
        db.session.commit()
        return user

@pytest.fixture
def auth_token(client, test_user):
    """Get authentication token for test user"""
    data = {
        'email': '<EMAIL>',
        'password': 'testpassword123'
    }
    
    response = client.post('/api/login', 
                         data=json.dumps(data),
                         content_type='application/json')
    
    result = json.loads(response.data)
    return result['tokens']['access_token']

class TestResumeUpload:
    """Test cases for resume upload functionality"""
    
    def test_upload_pdf_resume(self, client, auth_token):
        """Test uploading a PDF resume"""
        # Create a fake PDF file
        pdf_content = b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n'
        
        data = {
            'resume': (BytesIO(pdf_content), 'test_resume.pdf'),
            'title': 'Test Resume'
        }
        
        response = client.post('/api/upload_resume',
                             data=data,
                             headers={'Authorization': f'Bearer {auth_token}'},
                             content_type='multipart/form-data')
        
        assert response.status_code == 201
        result = json.loads(response.data)
        assert result['success'] is True
        assert 'Resume uploaded' in result['message']
        assert 'resume' in result
    
    def test_upload_without_authentication(self, client):
        """Test uploading without authentication token"""
        data = {
            'resume': (BytesIO(b'test content'), 'test.pdf')
        }
        
        response = client.post('/api/upload_resume',
                             data=data,
                             content_type='multipart/form-data')
        
        assert response.status_code == 401
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'authorization_required' in result['error']
    
    def test_upload_invalid_file_type(self, client, auth_token):
        """Test uploading invalid file type"""
        data = {
            'resume': (BytesIO(b'test content'), 'test.txt')
        }
        
        response = client.post('/api/upload_resume',
                             data=data,
                             headers={'Authorization': f'Bearer {auth_token}'},
                             content_type='multipart/form-data')
        
        assert response.status_code == 400
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'Unsupported file type' in result['message']
    
    def test_upload_no_file(self, client, auth_token):
        """Test uploading without file"""
        response = client.post('/api/upload_resume',
                             headers={'Authorization': f'Bearer {auth_token}'},
                             content_type='multipart/form-data')
        
        assert response.status_code == 400
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'No resume file provided' in result['message']
    
    def test_get_user_resumes(self, client, auth_token, app):
        """Test getting user's resumes"""
        # First upload a resume
        with app.app_context():
            user = User.query.filter_by(email='<EMAIL>').first()
            resume = Resume(
                user_id=user.id,
                original_filename='test.pdf',
                file_path='/fake/path/test.pdf',
                file_size=1024,
                file_type='pdf'
            )
            db.session.add(resume)
            db.session.commit()
        
        response = client.get('/api/resumes',
                            headers={'Authorization': f'Bearer {auth_token}'})
        
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result['success'] is True
        assert 'resumes' in result
        assert len(result['resumes']) == 1
        assert result['resumes'][0]['original_filename'] == 'test.pdf'
    
    def test_get_resume_details(self, client, auth_token, app):
        """Test getting specific resume details"""
        with app.app_context():
            user = User.query.filter_by(email='<EMAIL>').first()
            resume = Resume(
                user_id=user.id,
                original_filename='test.pdf',
                file_path='/fake/path/test.pdf',
                file_size=1024,
                file_type='pdf'
            )
            resume.extracted_text = 'Sample extracted text'
            db.session.add(resume)
            db.session.commit()
            resume_id = resume.id
        
        response = client.get(f'/api/resumes/{resume_id}',
                            headers={'Authorization': f'Bearer {auth_token}'})
        
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result['success'] is True
        assert 'resume' in result
        assert result['resume']['extracted_text'] == 'Sample extracted text'
    
    def test_delete_resume(self, client, auth_token, app):
        """Test deleting a resume"""
        with app.app_context():
            user = User.query.filter_by(email='<EMAIL>').first()
            resume = Resume(
                user_id=user.id,
                original_filename='test.pdf',
                file_path='/fake/path/test.pdf',
                file_size=1024,
                file_type='pdf'
            )
            db.session.add(resume)
            db.session.commit()
            resume_id = resume.id
        
        response = client.delete(f'/api/resumes/{resume_id}',
                               headers={'Authorization': f'Bearer {auth_token}'})
        
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result['success'] is True
        assert 'deleted successfully' in result['message']

class TestFileParser:
    """Test cases for file parsing functionality"""
    
    def test_validate_file_type_pdf(self):
        """Test PDF file type validation"""
        from services.file_parser import FileParser
        
        is_valid, file_type, error = FileParser.validate_file_type('resume.pdf')
        assert is_valid is True
        assert file_type == 'pdf'
        assert error == ''
    
    def test_validate_file_type_invalid(self):
        """Test invalid file type validation"""
        from services.file_parser import FileParser
        
        is_valid, file_type, error = FileParser.validate_file_type('resume.txt')
        assert is_valid is False
        assert 'Unsupported file type' in error
    
    def test_clean_extracted_text(self):
        """Test text cleaning functionality"""
        from services.file_parser import FileParser
        
        dirty_text = "  Line 1  \n\n\n  Line 2  \n   \n  Line 3  "
        cleaned = FileParser._clean_extracted_text(dirty_text)
        
        expected = "Line 1\nLine 2\nLine 3"
        assert cleaned == expected

class TestResumeModel:
    """Test cases for Resume model"""
    
    def test_resume_creation(self, app):
        """Test creating a resume record"""
        with app.app_context():
            user = User('John', 'Doe', '<EMAIL>', 'testpassword')
            db.session.add(user)
            db.session.commit()
            
            resume = Resume(
                user_id=user.id,
                original_filename='test.pdf',
                file_path='/path/to/test.pdf',
                file_size=1024,
                file_type='pdf',
                title='My Resume'
            )
            
            assert resume.original_filename == 'test.pdf'
            assert resume.file_type == 'pdf'
            assert resume.title == 'My Resume'
            assert resume.upload_status == 'processing'
    
    def test_file_size_formatting(self, app):
        """Test file size formatting"""
        with app.app_context():
            resume = Resume(
                user_id=1,
                original_filename='test.pdf',
                file_path='/path/to/test.pdf',
                file_size=1536,  # 1.5 KB
                file_type='pdf'
            )
            
            formatted_size = resume.get_file_size_formatted()
            assert '1.5 KB' in formatted_size
    
    def test_resume_to_dict(self, app):
        """Test resume serialization"""
        with app.app_context():
            resume = Resume(
                user_id=1,
                original_filename='test.pdf',
                file_path='/path/to/test.pdf',
                file_size=1024,
                file_type='pdf'
            )
            
            resume_dict = resume.to_dict()
            
            assert 'id' in resume_dict
            assert resume_dict['original_filename'] == 'test.pdf'
            assert resume_dict['file_type'] == 'pdf'
            assert resume_dict['file_size'] == 1024
            assert 'file_size_formatted' in resume_dict

if __name__ == '__main__':
    pytest.main([__file__])
