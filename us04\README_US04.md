# US-04: Job Description Upload System 📄

## 🎯 What We Built
A complete job description upload and management system that builds on US-01, US-02 & US-03:
- **Text Upload**: Secure upload of job description text via textarea input
- **Text Storage**: Save JD text directly to PostgreSQL database
- **JD Management**: View, edit, delete, and manage job descriptions
- **Character Counting**: Real-time character and word count with validation
- **Database Integration**: Store JD metadata, text, and user association
- **Security**: JWT-protected endpoints with text validation and length limits

## 📁 Project Structure
```
us04/
├── backend/
│   ├── app.py                    # 🚀 Flask app with JD endpoints
│   ├── models.py                 # 📊 User + Resume + JobDescription models
│   ├── config.py                 # ⚙️ JD configuration (length limits)
│   ├── routes/
│   │   ├── us04_auth_routes.py   # 🔐 Authentication (from US-03)
│   │   └── us04_jd_routes.py     # 📄 Job description CRUD endpoints
│   ├── requirements.txt          # 📦 Dependencies
│   └── .env                      # 🔧 Environment variables
├── frontend/
│   ├── us04_landing.html         # 🏠 Landing page
│   ├── us04_register.html        # 📝 Registration page
│   ├── us04_login.html           # 🔐 Login page
│   ├── us04_dashboard.html       # 📊 Dashboard with JD list
│   ├── us04_add_jd.html          # 📄 Add job description page
│   └── static/
│       ├── css/
│       │   └── us04_styles.css   # 🎨 Styles with JD UI
│       └── js/
│           ├── us04_register.js  # ⚡ Registration logic
│           ├── us04_login.js     # 🔑 Login logic
│           ├── us04_dashboard.js # 📊 Dashboard with JD management
│           └── us04_add_jd.js    # 📄 Add JD with character counter
├── tests/
│   └── test_us04_jd.py           # 🧪 JD upload and management tests
└── README_US04.md                # 📖 This documentation
```

## 🔧 Technology Stack
- **Backend**: Flask 2.3.3, Flask-JWT-Extended, SQLAlchemy
- **Text Processing**: Built-in Python text processing, word/character counting
- **Frontend**: HTML5, CSS3, Vanilla JavaScript with real-time character counter
- **Security**: JWT tokens, text validation, length limits (50-50,000 chars)
- **Testing**: pytest with JD CRUD testing
- **Database**: SQLite with JD text storage and metadata

## 🚀 Complete JD Upload Flow

### 1. Authentication → JD Upload Flow
1. User logs in (US-02 functionality)
2. Accesses dashboard with "Add Job Description" button
3. Navigates to `/add-job-description` page
4. Enters job title, company name (optional), and job description text
5. Real-time character/word counting with validation
6. Submits form with text validation
7. Backend saves text to database
8. User redirected to dashboard with updated JD list

### 2. Text Processing Pipeline
1. **Validation**: Check title (3-255 chars), text (50-50,000 chars), company name
2. **Storage**: Save text directly to database with metadata
3. **Counting**: Calculate word count and character count
4. **Database**: Create JobDescription record with all data

### 3. JD Management
1. **Dashboard**: View all job descriptions with metadata
2. **Details**: Click to view full job description text
3. **Delete**: Remove JD from database
4. **Edit**: Update existing job descriptions (basic implementation)

## 📊 Database Schema (Enhanced from US-03)
```sql
-- Users table (from US-02)
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Resumes table (from US-03)
CREATE TABLE resumes (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER NOT NULL,
    file_type VARCHAR(10) NOT NULL,
    extracted_text TEXT,
    title VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    upload_status VARCHAR(20) DEFAULT 'processing',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- NEW: Job Descriptions table
CREATE TABLE job_descriptions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) NOT NULL,
    title VARCHAR(255) NOT NULL,
    company_name VARCHAR(255),
    job_text TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    word_count INTEGER DEFAULT 0,
    character_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 Quick Setup & Testing

### 1. Install Dependencies
```bash
cd us04/backend
pip install -r requirements.txt
```

### 2. Run Application
```bash
python app.py
```

### 3. Test the Complete Flow
1. **Register/Login**: Create account and login
2. **Dashboard**: http://localhost:5000/dashboard
3. **Add JD**: Click "Add Job Description" → http://localhost:5000/add-job-description
4. **Test Upload**: Enter job title, company, and description text
5. **View Results**: Return to dashboard to see saved job description
6. **Manage**: View details, delete job descriptions

### 4. Test Text Validation
- **Minimum**: Try entering less than 50 characters
- **Maximum**: Try entering more than 50,000 characters
- **Title**: Test empty title or very long title
- **Character Counter**: Watch real-time counting

### 5. Run Tests
```bash
cd ../tests
pytest test_us04_jd.py -v
```

## 📝 API Endpoints

### POST /api/upload_jd (Protected)
Upload/create job description
```bash
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "title": "Senior Software Engineer",
  "company_name": "Tech Corp Inc.",
  "job_text": "We are seeking a talented Senior Software Engineer..."
}
```

**Success Response (201)**:
```json
{
  "success": true,
  "message": "Job description saved successfully",
  "job_description": {
    "id": 1,
    "title": "Senior Software Engineer",
    "company_name": "Tech Corp Inc.",
    "word_count": 125,
    "character_count": 850,
    "created_at": "2025-07-25T10:30:00",
    "job_text": "We are seeking a talented Senior Software Engineer..."
  }
}
```

### GET /api/job_descriptions (Protected)
Get all user's job descriptions
```bash
Authorization: Bearer <access_token>
```

**Success Response (200)**:
```json
{
  "success": true,
  "job_descriptions": [
    {
      "id": 1,
      "title": "Senior Software Engineer",
      "company_name": "Tech Corp Inc.",
      "word_count": 125,
      "character_count": 850,
      "created_at": "2025-07-25T10:30:00"
    }
  ],
  "count": 1
}
```

### GET /api/job_descriptions/{id} (Protected)
Get specific job description with full text
```bash
Authorization: Bearer <access_token>
```

### PUT /api/job_descriptions/{id} (Protected)
Update job description
```bash
Authorization: Bearer <access_token>
Content-Type: application/json
```

### DELETE /api/job_descriptions/{id} (Protected)
Delete job description
```bash
Authorization: Bearer <access_token>
```

## 🎨 UI Features Implemented

✅ **Add JD Page** - Professional text input interface
- Large textarea with placeholder example
- Real-time character and word counting
- Character limit validation with visual feedback
- Optional company name field
- Form validation with error messages

✅ **Enhanced Dashboard** - JD management interface
- Job description list with metadata (title, company, word count, date)
- View JD details modal with full text
- Delete JD functionality
- Updated statistics (JD count)
- Quick action buttons

✅ **Text Management**
- Real-time character counting with warnings
- Word count calculation
- Text length validation (50-50,000 characters)
- Clean text storage and retrieval

## 🔒 Security Features

✅ **Text Upload Security**
- JWT authentication required for all JD endpoints
- Text length validation (50-50,000 characters)
- Input sanitization and validation
- User isolation (users can only access their own JDs)

✅ **Data Protection**
- Secure text storage in database
- Input validation to prevent injection attacks
- Error handling without sensitive data exposure
- Character limits to prevent abuse

## 🧪 Testing Coverage

✅ **JD Upload Tests**
- Successful job description creation
- Authentication requirement enforcement
- Text validation (length, required fields)
- Invalid data handling

✅ **JD Management Tests**
- Fetching user's job descriptions
- Getting specific JD details
- Updating job descriptions
- Deleting job descriptions
- User isolation verification

✅ **Model Tests**
- JobDescription creation and validation
- Word and character counting
- Text validation rules
- JD serialization

## 🔄 Text Processing Details

✅ **Text Validation**
- Title: 3-255 characters required
- Job Text: 50-50,000 characters required
- Company Name: Optional, max 255 characters
- Real-time validation feedback

✅ **Text Processing**
- Automatic word count calculation
- Character count with real-time updates
- Text trimming and cleaning
- Metadata storage (word/character counts)

## 🎓 Learning Objectives

After completing US-04, you'll understand:
- ✅ Text-based data upload and storage
- ✅ Real-time form validation with JavaScript
- ✅ Character counting and text processing
- ✅ CRUD operations for text data
- ✅ Database text storage and retrieval
- ✅ User interface for text management
- ✅ Form validation and error handling
- ✅ Testing text-based functionality

## 🔄 Integration with Previous US

US-04 builds on US-01, US-02 & US-03:
- ✅ Uses authentication system from US-02
- ✅ Extends User model with job description relationship
- ✅ Enhances dashboard with JD management
- ✅ Maintains consistent UI/UX design
- ✅ Complements resume upload from US-03

## 🔄 Next Steps
After US-04 is complete and tested:
1. **US-05**: Keyword parsing and extraction from both resumes and JDs
2. **US-06**: Resume-job matching algorithm
3. **US-07**: AI-powered suggestions and improvements
4. **US-08**: Enhanced dashboard with analytics

## 🚨 Important Notes
- Text is stored directly in database (no file system)
- Character limits prevent database abuse
- Word counting uses simple whitespace splitting
- JD text includes full formatting and line breaks
- Real-time validation provides immediate feedback

## ✅ US-04 Status: COMPLETE
- ✅ Job description upload system working
- ✅ Text validation and processing functional
- ✅ JD management implemented
- ✅ Dashboard enhanced with JD list
- ✅ Tests passing
- ✅ Ready for US-05 integration
