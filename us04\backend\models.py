from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import create_access_token, create_refresh_token
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import re
import os

db = SQLAlchemy()

class User(db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(255), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    last_login = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    resumes = db.relationship('Resume', backref='user', lazy=True, cascade='all, delete-orphan')
    job_descriptions = db.relationship('JobDescription', backref='user', lazy=True, cascade='all, delete-orphan')
    
    def __init__(self, first_name, last_name, email, password):
        self.first_name = first_name
        self.last_name = last_name
        self.email = email.lower().strip()
        self.set_password(password)
    
    def set_password(self, password):
        """Hash and set the password"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check if provided password matches the hash"""
        return check_password_hash(self.password_hash, password)
    
    def update_last_login(self):
        """Update last login timestamp"""
        self.last_login = datetime.utcnow()
        db.session.commit()
    
    def generate_tokens(self):
        """Generate JWT access and refresh tokens"""
        access_token = create_access_token(
            identity=self.id,
            additional_claims={
                'email': self.email,
                'first_name': self.first_name,
                'last_name': self.last_name
            }
        )
        refresh_token = create_refresh_token(identity=self.id)
        
        return {
            'access_token': access_token,
            'refresh_token': refresh_token,
            'token_type': 'Bearer',
            'expires_in': 86400
        }
    
    @staticmethod
    def validate_email(email):
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_password(password):
        """Validate password strength"""
        if len(password) < 8:
            return False, "Password must be at least 8 characters long"
        return True, "Password is valid"
    
    def to_dict(self):
        """Convert user object to dictionary (excluding password)"""
        return {
            'id': self.id,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'email': self.email,
            'is_active': self.is_active,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'resume_count': len(self.resumes),
            'job_description_count': len(self.job_descriptions)
        }
    
    def __repr__(self):
        return f'<User {self.email}>'

class Resume(db.Model):
    __tablename__ = 'resumes'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # File information
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer, nullable=False)
    file_type = db.Column(db.String(10), nullable=False)
    
    # Parsed content
    extracted_text = db.Column(db.Text)
    
    # Metadata
    title = db.Column(db.String(255))
    is_active = db.Column(db.Boolean, default=True)
    upload_status = db.Column(db.String(20), default='processing')
    error_message = db.Column(db.Text)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, user_id, original_filename, file_path, file_size, file_type, title=None):
        self.user_id = user_id
        self.original_filename = original_filename
        self.file_path = file_path
        self.file_size = file_size
        self.file_type = file_type
        self.title = title or original_filename
    
    def get_file_size_formatted(self):
        """Return formatted file size"""
        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{self.file_size / 1024:.1f} KB"
        else:
            return f"{self.file_size / (1024 * 1024):.1f} MB"
    
    def delete_file(self):
        """Delete the physical file from filesystem"""
        try:
            if os.path.exists(self.file_path):
                os.remove(self.file_path)
                return True
        except Exception as e:
            print(f"Error deleting file {self.file_path}: {e}")
        return False
    
    def to_dict(self):
        """Convert resume object to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'file_size_formatted': self.get_file_size_formatted(),
            'file_type': self.file_type,
            'title': self.title,
            'is_active': self.is_active,
            'upload_status': self.upload_status,
            'error_message': self.error_message,
            'has_extracted_text': bool(self.extracted_text),
            'text_length': len(self.extracted_text) if self.extracted_text else 0,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<Resume {self.original_filename} by User {self.user_id}>'

class JobDescription(db.Model):
    __tablename__ = 'job_descriptions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Job Description Content
    title = db.Column(db.String(255), nullable=False)
    company_name = db.Column(db.String(255))
    job_text = db.Column(db.Text, nullable=False)
    
    # Metadata
    is_active = db.Column(db.Boolean, default=True)
    word_count = db.Column(db.Integer, default=0)
    character_count = db.Column(db.Integer, default=0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, user_id, title, job_text, company_name=None):
        self.user_id = user_id
        self.title = title.strip()
        self.job_text = job_text.strip()
        self.company_name = company_name.strip() if company_name else None
        self.update_counts()
    
    def update_counts(self):
        """Update word and character counts"""
        if self.job_text:
            self.character_count = len(self.job_text)
            # Simple word count (split by whitespace)
            self.word_count = len(self.job_text.split())
    
    @staticmethod
    def validate_job_description(title, job_text, company_name=None):
        """Validate job description data"""
        errors = []
        
        # Title validation
        if not title or not title.strip():
            errors.append('Job title is required')
        elif len(title.strip()) < 3:
            errors.append('Job title must be at least 3 characters long')
        elif len(title.strip()) > 255:
            errors.append('Job title must be less than 255 characters')
        
        # Job text validation
        if not job_text or not job_text.strip():
            errors.append('Job description text is required')
        elif len(job_text.strip()) < 50:
            errors.append('Job description must be at least 50 characters long')
        elif len(job_text.strip()) > 50000:
            errors.append('Job description must be less than 50,000 characters')
        
        # Company name validation (optional)
        if company_name and len(company_name.strip()) > 255:
            errors.append('Company name must be less than 255 characters')
        
        return errors
    
    def to_dict(self, include_text=False):
        """Convert job description object to dictionary"""
        data = {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'company_name': self.company_name,
            'is_active': self.is_active,
            'word_count': self.word_count,
            'character_count': self.character_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        # Include full text only if requested (for detailed view)
        if include_text:
            data['job_text'] = self.job_text
        
        return data
    
    def __repr__(self):
        return f'<JobDescription {self.title} by User {self.user_id}>'
