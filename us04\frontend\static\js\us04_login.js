// Dr. Resume US-04 Login JavaScript (same as US-03)

document.addEventListener('DOMContentLoaded', function() {
    console.log('🩺 Dr. Resume Login Page Loaded (US-04)');
    
    const token = localStorage.getItem('dr_resume_token');
    if (token) {
        verifyTokenAndRedirect();
        return;
    }
    
    const form = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const alertContainer = document.getElementById('alertContainer');
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        clearAlerts();
        
        const formData = new FormData(form);
        const data = {
            email: formData.get('email').trim(),
            password: formData.get('password')
        };
        
        const validationErrors = validateForm(data);
        if (validationErrors.length > 0) {
            showAlert('error', 'Please fix the following errors:', validationErrors);
            return;
        }
        
        setLoading(true);
        
        try {
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                showAlert('success', result.message);
                
                localStorage.setItem('dr_resume_token', result.tokens.access_token);
                localStorage.setItem('dr_resume_refresh_token', result.tokens.refresh_token);
                localStorage.setItem('dr_resume_user', JSON.stringify(result.user));
                
                console.log('✅ Login successful, token saved');
                
                setTimeout(() => { window.location.href = '/dashboard'; }, 1000);
                
            } else {
                showAlert('error', result.message);
            }
            
        } catch (error) {
            console.error('Login error:', error);
            showAlert('error', 'Network error. Please check your connection and try again.');
        } finally {
            setLoading(false);
        }
    });
});

async function verifyTokenAndRedirect() {
    try {
        const token = localStorage.getItem('dr_resume_token');
        
        const response = await fetch('/api/profile', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            console.log('✅ Valid token found, redirecting to dashboard');
            window.location.href = '/dashboard';
        } else {
            console.log('❌ Invalid token, clearing storage');
            localStorage.removeItem('dr_resume_token');
            localStorage.removeItem('dr_resume_refresh_token');
            localStorage.removeItem('dr_resume_user');
        }
        
    } catch (error) {
        console.error('Token verification error:', error);
        localStorage.removeItem('dr_resume_token');
        localStorage.removeItem('dr_resume_refresh_token');
        localStorage.removeItem('dr_resume_user');
    }
}

function validateForm(data) {
    const errors = [];
    
    if (!data.email) errors.push('Email is required');
    if (!data.password) errors.push('Password is required');
    
    if (data.email && !isValidEmail(data.email)) {
        errors.push('Please enter a valid email address');
    }
    
    return errors;
}

function isValidEmail(email) {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
}

function showAlert(type, message, errors = []) {
    const alertContainer = document.getElementById('alertContainer');
    
    let alertHTML = `<div class="alert alert-${type}">`;
    alertHTML += `<strong>${message}</strong>`;
    
    if (errors.length > 0) {
        alertHTML += '<ul style="margin: 8px 0 0 20px;">';
        errors.forEach(error => {
            alertHTML += `<li>${error}</li>`;
        });
        alertHTML += '</ul>';
    }
    
    alertHTML += '</div>';
    
    alertContainer.innerHTML = alertHTML;
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function clearAlerts() {
    document.getElementById('alertContainer').innerHTML = '';
}

function setLoading(loading) {
    const loginBtn = document.getElementById('loginBtn');
    
    if (loading) {
        loginBtn.disabled = true;
        loginBtn.classList.add('loading');
        loginBtn.textContent = 'Signing In...';
    } else {
        loginBtn.disabled = false;
        loginBtn.classList.remove('loading');
        loginBtn.textContent = 'Login';
    }
}
