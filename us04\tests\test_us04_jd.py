import pytest
import json
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app import create_app
from models import db, User, JobDescription

@pytest.fixture
def app():
    """Create and configure a test app"""
    app = create_app()
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    app.config['JWT_SECRET_KEY'] = 'test-jwt-secret'
    
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()

@pytest.fixture
def client(app):
    """Create a test client"""
    return app.test_client()

@pytest.fixture
def test_user(app):
    """Create a test user"""
    with app.app_context():
        user = User('Test', 'User', '<EMAIL>', 'testpassword123')
        db.session.add(user)
        db.session.commit()
        return user

@pytest.fixture
def auth_token(client, test_user):
    """Get authentication token for test user"""
    data = {
        'email': '<EMAIL>',
        'password': 'testpassword123'
    }
    
    response = client.post('/api/login', 
                         data=json.dumps(data),
                         content_type='application/json')
    
    result = json.loads(response.data)
    return result['tokens']['access_token']

class TestJobDescriptionUpload:
    """Test cases for job description upload functionality"""
    
    def test_upload_job_description(self, client, auth_token):
        """Test uploading a job description"""
        data = {
            'title': 'Software Engineer',
            'company_name': 'Tech Corp',
            'job_text': 'We are looking for a skilled software engineer with experience in Python, JavaScript, and modern web frameworks. The ideal candidate will have 3+ years of experience and strong problem-solving skills.'
        }
        
        response = client.post('/api/upload_jd',
                             data=json.dumps(data),
                             headers={'Authorization': f'Bearer {auth_token}'},
                             content_type='application/json')
        
        assert response.status_code == 201
        result = json.loads(response.data)
        assert result['success'] is True
        assert 'Job description saved successfully' in result['message']
        assert 'job_description' in result
        assert result['job_description']['title'] == 'Software Engineer'
        assert result['job_description']['company_name'] == 'Tech Corp'
    
    def test_upload_without_authentication(self, client):
        """Test uploading without authentication token"""
        data = {
            'title': 'Software Engineer',
            'job_text': 'Test job description'
        }
        
        response = client.post('/api/upload_jd',
                             data=json.dumps(data),
                             content_type='application/json')
        
        assert response.status_code == 401
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'authorization_required' in result['error']
    
    def test_upload_invalid_data(self, client, auth_token):
        """Test uploading with invalid data"""
        data = {
            'title': '',  # Empty title
            'job_text': 'Short'  # Too short
        }
        
        response = client.post('/api/upload_jd',
                             data=json.dumps(data),
                             headers={'Authorization': f'Bearer {auth_token}'},
                             content_type='application/json')
        
        assert response.status_code == 400
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'Validation failed' in result['message']
        assert 'errors' in result
    
    def test_upload_no_data(self, client, auth_token):
        """Test uploading without data"""
        response = client.post('/api/upload_jd',
                             headers={'Authorization': f'Bearer {auth_token}'},
                             content_type='application/json')
        
        assert response.status_code == 400
        result = json.loads(response.data)
        assert result['success'] is False
        assert 'No data provided' in result['message']
    
    def test_get_user_job_descriptions(self, client, auth_token, app):
        """Test getting user's job descriptions"""
        # First create a job description
        with app.app_context():
            user = User.query.filter_by(email='<EMAIL>').first()
            jd = JobDescription(
                user_id=user.id,
                title='Test Job',
                job_text='This is a test job description with enough characters to meet the minimum requirement.'
            )
            db.session.add(jd)
            db.session.commit()
        
        response = client.get('/api/job_descriptions',
                            headers={'Authorization': f'Bearer {auth_token}'})
        
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result['success'] is True
        assert 'job_descriptions' in result
        assert len(result['job_descriptions']) == 1
        assert result['job_descriptions'][0]['title'] == 'Test Job'
    
    def test_get_job_description_details(self, client, auth_token, app):
        """Test getting specific job description details"""
        with app.app_context():
            user = User.query.filter_by(email='<EMAIL>').first()
            jd = JobDescription(
                user_id=user.id,
                title='Test Job',
                job_text='This is a detailed test job description with enough characters to meet the minimum requirement for testing purposes.'
            )
            db.session.add(jd)
            db.session.commit()
            jd_id = jd.id
        
        response = client.get(f'/api/job_descriptions/{jd_id}',
                            headers={'Authorization': f'Bearer {auth_token}'})
        
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result['success'] is True
        assert 'job_description' in result
        assert result['job_description']['job_text'] is not None
    
    def test_delete_job_description(self, client, auth_token, app):
        """Test deleting a job description"""
        with app.app_context():
            user = User.query.filter_by(email='<EMAIL>').first()
            jd = JobDescription(
                user_id=user.id,
                title='Test Job',
                job_text='This is a test job description with enough characters to meet the minimum requirement.'
            )
            db.session.add(jd)
            db.session.commit()
            jd_id = jd.id
        
        response = client.delete(f'/api/job_descriptions/{jd_id}',
                               headers={'Authorization': f'Bearer {auth_token}'})
        
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result['success'] is True
        assert 'deleted successfully' in result['message']
    
    def test_update_job_description(self, client, auth_token, app):
        """Test updating a job description"""
        with app.app_context():
            user = User.query.filter_by(email='<EMAIL>').first()
            jd = JobDescription(
                user_id=user.id,
                title='Original Title',
                job_text='This is the original job description with enough characters to meet the minimum requirement.'
            )
            db.session.add(jd)
            db.session.commit()
            jd_id = jd.id
        
        update_data = {
            'title': 'Updated Title',
            'job_text': 'This is the updated job description with enough characters to meet the minimum requirement for testing.'
        }
        
        response = client.put(f'/api/job_descriptions/{jd_id}',
                            data=json.dumps(update_data),
                            headers={'Authorization': f'Bearer {auth_token}'},
                            content_type='application/json')
        
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result['success'] is True
        assert result['job_description']['title'] == 'Updated Title'

class TestJobDescriptionModel:
    """Test cases for JobDescription model"""
    
    def test_job_description_creation(self, app):
        """Test creating a job description record"""
        with app.app_context():
            user = User('John', 'Doe', '<EMAIL>', 'testpassword')
            db.session.add(user)
            db.session.commit()
            
            jd = JobDescription(
                user_id=user.id,
                title='Software Engineer',
                job_text='This is a test job description with enough characters to meet the minimum requirement.',
                company_name='Tech Corp'
            )
            
            assert jd.title == 'Software Engineer'
            assert jd.company_name == 'Tech Corp'
            assert jd.word_count > 0
            assert jd.character_count > 0
    
    def test_job_description_validation(self):
        """Test job description validation"""
        # Valid data
        errors = JobDescription.validate_job_description(
            'Software Engineer',
            'This is a valid job description with enough characters to meet the minimum requirement.',
            'Tech Corp'
        )
        assert len(errors) == 0
        
        # Invalid title
        errors = JobDescription.validate_job_description(
            '',
            'Valid job description text with enough characters.',
            'Tech Corp'
        )
        assert len(errors) > 0
        assert any('title' in error.lower() for error in errors)
        
        # Invalid job text (too short)
        errors = JobDescription.validate_job_description(
            'Valid Title',
            'Short',
            'Tech Corp'
        )
        assert len(errors) > 0
        assert any('50 characters' in error for error in errors)
    
    def test_word_and_character_count(self, app):
        """Test word and character counting"""
        with app.app_context():
            jd = JobDescription(
                user_id=1,
                title='Test Job',
                job_text='This is a test job description with exactly ten words here.'
            )
            
            assert jd.word_count == 10
            assert jd.character_count == len(jd.job_text)
    
    def test_job_description_to_dict(self, app):
        """Test job description serialization"""
        with app.app_context():
            jd = JobDescription(
                user_id=1,
                title='Software Engineer',
                job_text='This is a test job description with enough characters to meet the minimum requirement.',
                company_name='Tech Corp'
            )
            
            jd_dict = jd.to_dict()
            
            assert 'id' in jd_dict
            assert jd_dict['title'] == 'Software Engineer'
            assert jd_dict['company_name'] == 'Tech Corp'
            assert jd_dict['word_count'] > 0
            assert jd_dict['character_count'] > 0
            assert 'job_text' not in jd_dict  # Should not include text by default
            
            # Test with text included
            jd_dict_with_text = jd.to_dict(include_text=True)
            assert 'job_text' in jd_dict_with_text

if __name__ == '__main__':
    pytest.main([__file__])
