# US-05: Keyword Parsing Backend (Local Script)

## Overview
US-05 implements automatic keyword extraction from resumes and job descriptions using spaCy and NLTK. The system automatically runs after resume or job description upload, extracts keywords, and saves them to the database for later use in suggestions and scoring.

## Features

### ✅ Automatic Keyword Extraction
- **Triggers**: Automatically runs after successful resume upload or job description creation
- **Technologies**: spaCy, NLTK, scikit-learn
- **Categories**: Technical skills, soft skills, and other keywords
- **Storage**: Keywords saved to PostgreSQL database as JSON

### ✅ Integration Points
- **Resume Upload**: Keywords extracted after successful text parsing
- **Job Description Creation**: Keywords extracted after successful creation
- **Job Description Update**: Keywords re-extracted when content changes
- **Job Description Duplication**: Keywords extracted for duplicated content

## Technical Implementation

### Backend Components

#### 1. Keyword Parser Service (`services/keyword_parser.py`)
- **SpaCy Integration**: Uses `en_core_web_sm` model for NLP processing
- **NLTK Integration**: Fallback for keyword extraction
- **TF-IDF Analysis**: Statistical keyword importance using scikit-learn
- **Multi-method Approach**: Combines multiple extraction techniques

#### 2. Database Schema
```sql
-- Resume table additions
keywords_extracted BOOLEAN DEFAULT FALSE
technical_skills TEXT  -- JSON array
soft_skills TEXT       -- JSON array  
other_keywords TEXT     -- JSON array
keyword_count INTEGER DEFAULT 0

-- JobDescription table additions  
keywords_extracted BOOLEAN DEFAULT FALSE
technical_skills TEXT  -- JSON array
soft_skills TEXT       -- JSON array
other_keywords TEXT     -- JSON array
keyword_count INTEGER DEFAULT 0
```

#### 3. Automatic Integration
- **Resume Upload Route** (`routes/us05_upload_routes.py`):
  ```python
  # After successful text extraction
  keywords = keyword_parser.extract_keywords(extracted_text)
  resume.set_keywords(
      technical_skills=keywords['technical_skills'],
      soft_skills=keywords['soft_skills'], 
      other_keywords=keywords['other_keywords']
  )
  ```

- **Job Description Routes** (`routes/us05_jd_routes.py`):
  ```python
  # After successful creation/update
  keywords = keyword_parser.extract_keywords(job_text)
  job_description.set_keywords(
      technical_skills=keywords['technical_skills'],
      soft_skills=keywords['soft_skills'],
      other_keywords=keywords['other_keywords']
  )
  ```

## API Endpoints

### Automatic Extraction (No Direct UI)
Keywords are automatically extracted and included in responses:

```json
{
  "success": true,
  "message": "Resume uploaded and processed successfully",
  "resume": {
    "id": 1,
    "keywords_extracted": true,
    "keyword_count": 25,
    "keywords": {
      "technical_skills": ["python", "flask", "postgresql"],
      "soft_skills": ["communication", "leadership"],
      "other_keywords": ["developer", "experience", "project"]
    }
  },
  "keywords_extracted": true
}
```

### Manual Keyword Extraction Endpoints
- `POST /api/parse_resume_keywords/<resume_id>` - Extract keywords for specific resume
- `POST /api/parse_jd_keywords/<jd_id>` - Extract keywords for specific job description
- `POST /api/parse_all_resume_keywords` - Batch process all resumes
- `POST /api/parse_all_jd_keywords` - Batch process all job descriptions

## Workflow

### Resume Upload Workflow
1. User uploads resume file
2. File is parsed and text extracted
3. **Automatic keyword extraction triggered**
4. Keywords categorized and saved to database
5. Response includes keyword information

### Job Description Workflow  
1. User creates/updates job description
2. Job description saved to database
3. **Automatic keyword extraction triggered**
4. Keywords categorized and saved to database
5. Response includes keyword information

## Error Handling
- Keyword extraction failures don't prevent upload/creation
- Errors logged but don't affect main workflow
- Graceful fallback if NLP libraries unavailable

## Future Integration
Keywords extracted in US-05 will be used in:
- **US-06**: Resume-Job matching and scoring
- **US-07**: Keyword suggestions and recommendations
- **US-08**: Analytics and insights
- **US-09**: Advanced matching algorithms
- **US-10**: Production deployment

## Dependencies
```
spacy>=3.7.2
nltk>=3.8.1
scikit-learn>=1.3.0
```

## Installation
```bash
# Install spaCy model
python -m spacy download en_core_web_sm

# Install NLTK data
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords')"
```

## Testing
The automatic keyword extraction has been tested and verified:
- ✅ Resume uploads automatically extract keywords
- ✅ Job description creation automatically extracts keywords  
- ✅ Keywords saved to database with proper categorization
- ✅ Error handling prevents workflow interruption
- ✅ Manual extraction endpoints available for batch processing

## Logs
Keyword extraction activities are logged:
```
INFO:services.keyword_parser:Extracted 9 technical skills, 0 soft skills, 16 other keywords
INFO:app:Keywords automatically extracted for job description 3
```
