/* Dr. Resume US-04 Styles - Job Description Upload + Management */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 100%;
    max-width: 800px;
}

.header {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
}

.header h1 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.header .subtitle {
    font-size: 16px;
    opacity: 0.9;
    font-weight: 400;
}

.content {
    padding: 40px 30px;
}

.welcome-text {
    text-align: center;
    color: #6b7280;
    margin-bottom: 30px;
    font-size: 16px;
    line-height: 1.5;
}

.btn {
    width: 100%;
    padding: 16px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    margin-bottom: 12px;
}

.btn-primary {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(124, 58, 237, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #7c3aed;
    border: 2px solid #7c3aed;
}

.btn-secondary:hover {
    background: #7c3aed;
    color: white;
    transform: translateY(-2px);
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #374151;
    font-weight: 500;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.2s ease;
    background: #f9fafb;
}

.form-control:focus {
    outline: none;
    border-color: #7c3aed;
    background: white;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
}

/* File Upload Styles */
.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    background: #f9fafb;
    transition: all 0.2s ease;
    cursor: pointer;
    margin-bottom: 20px;
}

.upload-area:hover {
    border-color: #7c3aed;
    background: #f3f4f6;
}

.upload-area.dragover {
    border-color: #7c3aed;
    background: #ede9fe;
}

.upload-icon {
    font-size: 48px;
    color: #9ca3af;
    margin-bottom: 16px;
}

.upload-text {
    color: #6b7280;
    margin-bottom: 8px;
}

.upload-subtext {
    color: #9ca3af;
    font-size: 14px;
}

.file-input {
    display: none;
}

.file-info {
    background: #f3f4f6;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    display: none;
}

.file-name {
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
}

.file-size {
    color: #6b7280;
    font-size: 14px;
}

/* Progress Bar */
.progress-container {
    margin: 20px 0;
    display: none;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    margin-top: 8px;
    color: #6b7280;
    font-size: 14px;
}

/* Textarea Styles */
.textarea-container {
    position: relative;
}

.form-textarea {
    width: 100%;
    min-height: 300px;
    padding: 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    font-family: inherit;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.2s ease;
    background: #f9fafb;
}

.form-textarea:focus {
    outline: none;
    border-color: #7c3aed;
    background: white;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
}

.character-counter {
    position: absolute;
    bottom: 12px;
    right: 16px;
    background: rgba(255, 255, 255, 0.9);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #6b7280;
    border: 1px solid #e5e7eb;
}

.character-counter.warning {
    color: #f59e0b;
    border-color: #f59e0b;
}

.character-counter.error {
    color: #ef4444;
    border-color: #ef4444;
}

/* Alert Messages */
.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
}

.alert-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.alert-warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fcd34d;
}

/* Dashboard Styles */
.dashboard-container {
    max-width: 1200px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.dashboard-header {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    color: white;
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-nav {
    display: flex;
    gap: 20px;
    align-items: center;
}

.nav-item {
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.2);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.dashboard-content {
    padding: 40px;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.dashboard-card {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    transition: all 0.2s ease;
}

.dashboard-card:hover {
    border-color: #7c3aed;
    transform: translateY(-2px);
}

.card-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.card-description {
    color: #6b7280;
    margin-bottom: 20px;
    line-height: 1.5;
}

.card-button {
    background: #7c3aed;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.card-button:hover {
    background: #6d28d9;
}

/* Job Description List Styles */
.jd-list {
    margin-top: 30px;
}

.jd-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    transition: all 0.2s ease;
}

.jd-item:hover {
    border-color: #7c3aed;
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.1);
}

.jd-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.jd-title {
    font-weight: 600;
    color: #1f2937;
    font-size: 18px;
    margin-bottom: 4px;
}

.jd-company {
    color: #7c3aed;
    font-weight: 500;
    margin-bottom: 8px;
}

.jd-meta {
    color: #6b7280;
    font-size: 14px;
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.jd-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.btn-small {
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-view {
    background: #3b82f6;
    color: white;
}

.btn-view:hover {
    background: #2563eb;
}

.btn-edit {
    background: #10b981;
    color: white;
}

.btn-edit:hover {
    background: #059669;
}

.btn-delete {
    background: #ef4444;
    color: white;
}

.btn-delete:hover {
    background: #dc2626;
}

/* Loading State */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .dashboard-nav {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .jd-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .jd-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .container {
        max-width: none;
        margin: 10px;
    }
}

@media (max-width: 480px) {
    .container, .dashboard-container {
        margin: 10px;
        max-width: none;
    }
    
    .header, .dashboard-content {
        padding: 30px 20px;
    }
    
    .content {
        padding: 30px 20px;
    }
    
    .header h1 {
        font-size: 24px;
    }
    
    .form-textarea {
        min-height: 250px;
    }
}

/* Keyword Analysis Styles (NEW for US-05) */
.keyword-section {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
}

.keyword-section h3 {
    color: #1f2937;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.keyword-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
}

.keyword-tag {
    background: #f3f4f6;
    color: #374151;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.keyword-tag.technical {
    background: #dbeafe;
    color: #1e40af;
    border-color: #93c5fd;
}

.keyword-tag.soft {
    background: #dcfce7;
    color: #166534;
    border-color: #86efac;
}

.keyword-tag.other {
    background: #fef3c7;
    color: #92400e;
    border-color: #fcd34d;
}

.keyword-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.keyword-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.keyword-stat {
    text-align: center;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
}

.keyword-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #7c3aed;
    margin-bottom: 4px;
}

.keyword-stat-label {
    color: #6b7280;
    font-size: 14px;
}

.parse-button {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.parse-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.parse-button:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.parse-all-section {
    background: #f8fafc;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    text-align: center;
}

.parse-all-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 16px;
}

.keyword-empty {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 24px;
}

.keyword-analysis-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.2s ease;
}

.keyword-analysis-card:hover {
    border-color: #7c3aed;
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.1);
}

.keyword-analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.keyword-analysis-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
}

.keyword-analysis-meta {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 8px;
}

.keyword-analysis-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.keyword-count-badge {
    background: #7c3aed;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.no-keywords {
    background: #f3f4f6;
    color: #6b7280;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}
