from flask import Flask, render_template, send_from_directory
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>
from models import db
from routes.us05_auth_routes import auth_bp
from routes.us05_jd_routes import jd_bp
from routes.us05_upload_routes import upload_bp
from routes.us05_keyword_routes import keyword_bp
from routes.us06_matching_routes import matching_bp
from config import Config
import os

def create_app():
    """Application factory pattern"""
    app = Flask(__name__, 
                template_folder='../frontend',
                static_folder='../frontend/static')
    
    # Load configuration
    app.config.from_object(Config)
    
    # Initialize upload directories
    Config.init_app(app)
    
    # Initialize extensions
    db.init_app(app)
    CORS(app, origins=app.config['CORS_ORIGINS'])
    
    # Initialize JWT
    jwt = JWTManager(app)
    
    # JWT error handlers
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return {
            'success': False,
            'message': 'Token has expired',
            'error': 'token_expired'
        }, 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return {
            'success': False,
            'message': 'Invalid token',
            'error': 'invalid_token'
        }, 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return {
            'success': False,
            'message': 'Authorization token is required',
            'error': 'authorization_required'
        }, 401
    
    # Register blueprints
    app.register_blueprint(auth_bp)
    app.register_blueprint(jd_bp)
    app.register_blueprint(upload_bp)
    app.register_blueprint(keyword_bp)
    app.register_blueprint(matching_bp)
    
    # Frontend routes
    @app.route('/')
    def landing():
        """Serve the landing page"""
        return render_template('us06_landing.html')
    
    @app.route('/register')
    def register_page():
        """Serve the registration page"""
        return render_template('us06_register.html')

    @app.route('/login')
    def login_page():
        """Serve the login page"""
        return render_template('us06_login.html')

    @app.route('/dashboard')
    def dashboard_page():
        """Serve the dashboard page (protected)"""
        return render_template('us06_dashboard.html')

    @app.route('/add-job-description')
    def add_jd_page():
        """Serve the add job description page (protected)"""
        return render_template('us06_add_jd.html')

    @app.route('/upload')
    def upload_page():
        """Serve the upload page (protected)"""
        return render_template('us06_upload.html')
    
    @app.route('/keywords')
    def keywords_page():
        """Serve the keywords analysis page (protected)"""
        return render_template('us06_keywords.html')

    @app.route('/matching')
    def matching_page():
        """Serve the matching score page (protected)"""
        return render_template('us06_matching.html')

    # Health check endpoint
    @app.route('/health')
    def health_check():
        return {'status': 'healthy', 'service': 'Dr. Resume US-06'}, 200

    # Favicon route
    @app.route('/favicon.ico')
    def favicon():
        return '', 204  # No content for favicon
    
    # Create database tables
    with app.app_context():
        db.create_all()
        print("Database tables created successfully!")
    
    return app

if __name__ == '__main__':
    app = create_app()
    print("🚀 Dr. Resume US-06 Server Starting...")
    print("📱 Landing Page: http://localhost:5000")
    print("📝 Register Page: http://localhost:5000/register")
    print("🔐 Login Page: http://localhost:5000/login")
    print("📊 Dashboard: http://localhost:5000/dashboard")
    print("📄 Add Job Description: http://localhost:5000/add-job-description")
    print("📤 Upload Resume: http://localhost:5000/upload")
    print("🔍 Keywords Analysis: http://localhost:5000/keywords")
    print("🎯 Matching Score: http://localhost:5000/matching")
    print("💚 Health Check: http://localhost:5000/health")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
