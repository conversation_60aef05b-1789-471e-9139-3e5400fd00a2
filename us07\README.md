# US-06: Matching Score Backend (Local Script)

## 🎯 Overview
US-06 implements a comprehensive resume-job description matching system using the Jaccard similarity algorithm. The system compares keywords extracted in US-05 and calculates detailed matching scores with an intuitive frontend UI featuring progress bars and color-coded indicators.

## ✅ Features Implemented

### **Backend Matching Engine**
- **Jaccard Similarity Algorithm**: Industry-standard keyword matching
- **Weighted Scoring System**: Technical skills (50%), Soft skills (20%), Other keywords (30%)
- **Database Storage**: Match scores saved for history and analytics
- **Comprehensive API**: Multiple endpoints for different matching scenarios

### **Frontend UI Components**
- **Interactive Calculator**: Select resume and job description dropdowns
- **Real-time Progress Bars**: Visual representation of category scores
- **Color-coded Indicators**: Excellent (green), Good (blue), Fair (orange), Poor (red)
- **Match History**: Recent calculations with detailed information
- **Responsive Design**: Works on desktop and mobile devices

## 🏗️ Technical Architecture

### **Database Schema**
```sql
CREATE TABLE match_scores (
    id INTEGER PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    resume_id INTEGER REFERENCES resumes(id),
    job_description_id INTEGER REFERENCES job_descriptions(id),
    
    -- Scores (0-100%)
    overall_score FLOAT NOT NULL,
    technical_score FLOAT DEFAULT 0.0,
    soft_skills_score FLOAT DEFAULT 0.0,
    other_keywords_score FLOAT DEFAULT 0.0,
    
    -- Analysis details
    total_resume_keywords INTEGER DEFAULT 0,
    total_jd_keywords INTEGER DEFAULT 0,
    matched_keywords INTEGER DEFAULT 0,
    algorithm_used VARCHAR(50) DEFAULT 'jaccard',
    
    -- Metadata
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **Matching Algorithm**
```python
def calculate_jaccard_similarity(set1, set2):
    """
    Jaccard similarity = |A ∩ B| / |A ∪ B|
    """
    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))
    return (intersection / union) * 100 if union > 0 else 0.0

def calculate_weighted_score(technical, soft_skills, other):
    """
    Weighted average:
    - Technical skills: 50%
    - Soft skills: 20% 
    - Other keywords: 30%
    """
    return (technical * 0.5) + (soft_skills * 0.2) + (other * 0.3)
```

## 📡 API Endpoints

### **Core Matching**
- `POST /api/calculate_match` - Calculate match score between resume and JD
- `GET /api/match_history` - Get user's match score history
- `GET /api/match_score/<id>` - Get specific match score details

### **Bulk Operations**
- `POST /api/bulk_calculate` - Calculate multiple matches at once
- `GET /api/resume/<id>/matches` - Get all matches for a resume
- `GET /api/job_description/<id>/matches` - Get all matches for a JD

### **Example Request/Response**
```javascript
// Request
POST /api/calculate_match
{
    "resume_id": 1,
    "job_description_id": 2
}

// Response
{
    "success": true,
    "match_score": {
        "id": 1,
        "overall_score": 43.24,
        "technical_score": 70.0,
        "soft_skills_score": 33.33,
        "other_keywords_score": 5.26,
        "matched_keywords": 8,
        "algorithm_used": "jaccard"
    },
    "detailed_scores": {
        "technical_score": 70.0,
        "soft_skills_score": 33.33,
        "other_keywords_score": 5.26,
        "overall_score": 43.24
    },
    "keyword_analysis": {
        "total_resume_keywords": 14,
        "total_jd_keywords": 27,
        "matched_keywords": 8,
        "match_percentage": 29.63
    }
}
```

## 🎨 Frontend Components

### **Matching Calculator**
```html
<section class="match-calculator">
    <div class="calculator-form">
        <select id="resumeSelect">Resume options</select>
        <select id="jobSelect">Job description options</select>
        <button id="calculateBtn">Calculate Match Score</button>
    </div>
</section>
```

### **Score Display**
```html
<div class="score-display">
    <div class="overall-score">
        <div class="score-circle">43%</div>
    </div>
    <div class="detailed-scores">
        <div class="progress-bar">
            <div class="progress-fill" style="width: 70%"></div>
        </div>
    </div>
</div>
```

### **Color Coding System**
- **Excellent (80-100%)**: Green gradient `#10b981 → #059669`
- **Good (60-79%)**: Blue gradient `#3b82f6 → #2563eb`
- **Fair (40-59%)**: Orange gradient `#f59e0b → #d97706`
- **Poor (0-39%)**: Red gradient `#ef4444 → #dc2626`

## 🔄 Integration Flow

### **Complete Workflow**
```
1. User uploads resume → US-03 (File parsing)
2. Keywords extracted → US-05 (Keyword parsing)
3. User creates job description → US-04 (JD storage)
4. Keywords extracted → US-05 (Keyword parsing)
5. User calculates match → US-06 (Matching score)
6. Results displayed → Frontend UI
7. History saved → Database
```

### **Data Dependencies**
- **Requires**: US-05 keyword extraction completed
- **Provides**: Match scores for US-07 suggestions
- **Database**: MatchScore model with relationships

## 🧪 Testing Results

### **Backend Testing**
```bash
# Test match calculation
curl -X POST http://localhost:5000/api/calculate_match \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"resume_id": 1, "job_description_id": 1}'

# Result: 43.24% overall score
# - Technical: 70.0% (excellent)
# - Soft Skills: 33.33% (fair)
# - Other Keywords: 5.26% (poor)
```

### **Frontend Testing**
- ✅ Resume/JD selection dropdowns populate correctly
- ✅ Match calculation triggers API call
- ✅ Progress bars animate to correct percentages
- ✅ Color coding applies based on score ranges
- ✅ Match history displays recent calculations
- ✅ Responsive design works on mobile

## 📚 Educational Guide

### **For Complete Beginners**

#### **What is Jaccard Similarity?**
Jaccard similarity measures how similar two sets are by comparing their intersection and union:
- **Intersection**: Keywords that appear in BOTH resume and job description
- **Union**: ALL unique keywords from resume AND job description
- **Formula**: Similarity = (Common keywords) / (Total unique keywords)

#### **Why Weighted Scoring?**
Different keyword categories have different importance:
- **Technical skills** (50%): Most important for job matching
- **Soft skills** (20%): Important but less specific
- **Other keywords** (30%): Context and industry terms

#### **How to Build This**
1. **Start with US-05**: Ensure keywords are extracted
2. **Create MatchScore model**: Database table for storing results
3. **Implement algorithm**: Jaccard similarity calculation
4. **Build API endpoints**: REST API for frontend communication
5. **Create frontend UI**: Interactive matching interface
6. **Test thoroughly**: Verify calculations and UI behavior

### **File Structure**
```
us06/
├── backend/
│   ├── app.py                     # Main Flask application
│   ├── models.py                  # MatchScore database model
│   ├── services/
│   │   └── matching_service.py    # Jaccard algorithm implementation
│   └── routes/
│       └── us06_matching_routes.py # API endpoints
├── frontend/
│   ├── us06_matching.html         # Matching page UI
│   └── static/
│       ├── css/us06_styles.css    # Matching page styles
│       └── js/us06_matching.js    # Frontend logic
└── README.md                      # This documentation
```

## 🚀 Next Steps

### **Integration with US-07**
The matching scores calculated here will be used in US-07 for:
- **Missing keyword identification**: Keywords in JD but not in resume
- **Improvement suggestions**: Recommendations based on low scores
- **Priority ranking**: Which keywords to focus on first

### **Future Enhancements**
- **Multiple algorithms**: Cosine similarity, TF-IDF scoring
- **Machine learning**: AI-powered matching improvements
- **Industry-specific weights**: Different scoring for different job types
- **Batch processing**: Bulk matching for multiple resumes/JDs

## ✅ Completion Checklist

- [x] **Backend**: Jaccard similarity algorithm implemented
- [x] **Database**: MatchScore model with relationships
- [x] **API**: Complete REST endpoints for matching
- [x] **Frontend**: Interactive UI with progress bars
- [x] **Styling**: Color-coded indicators and responsive design
- [x] **Testing**: Backend and frontend functionality verified
- [x] **Integration**: Works with US-05 keyword extraction
- [x] **Documentation**: Complete educational guide

**US-06 is 100% complete and ready for integration with US-07!** 🎉
