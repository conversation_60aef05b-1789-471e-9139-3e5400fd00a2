"""
US-07.1: Premium Suggestions Service
OpenAI-powered suggestions for enhanced resume optimization
"""

import logging
import os
import json
from typing import Dict, List, Optional
from models import db, Resume, JobDescription, MatchScore
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("OpenAI library not installed. Premium suggestions will use fallback logic.")


class PremiumSuggestionsService:
    """
    Service for generating AI-powered premium suggestions using OpenAI API
    """
    
    def __init__(self):
        self.logger = logger
        self.openai_available = OPENAI_AVAILABLE
        
        # Initialize OpenAI if available
        if self.openai_available:
            api_key = os.getenv('OPENAI_API_KEY')
            if api_key:
                self.client = OpenAI(api_key=api_key)
                self.logger.info("OpenAI API initialized successfully")
            else:
                self.openai_available = False
                self.client = None
                self.logger.warning("OPENAI_API_KEY not found in environment variables")
        else:
            self.client = None
        
        # Fallback templates for when OpenAI is not available
        self.fallback_templates = {
            'comprehensive': self._get_comprehensive_template(),
            'quick': self._get_quick_template(),
            'targeted': self._get_targeted_template()
        }
    
    def generate_premium_suggestions(self, resume_id: int, job_description_id: int, 
                                   user_id: int, suggestion_type: str = 'comprehensive') -> Dict:
        """
        Generate premium AI-powered suggestions
        
        Args:
            resume_id: ID of the resume
            job_description_id: ID of the job description
            user_id: ID of the user
            suggestion_type: Type of suggestions ('comprehensive', 'quick', 'targeted')
            
        Returns:
            Dict containing AI-generated suggestions and recommendations
        """
        try:
            # Get resume and job description
            resume = Resume.query.filter_by(
                id=resume_id,
                user_id=user_id,
                is_active=True
            ).first()
            
            job_description = JobDescription.query.filter_by(
                id=job_description_id,
                user_id=user_id,
                is_active=True
            ).first()
            
            if not resume or not job_description:
                raise ValueError("Resume or job description not found")
            
            if not resume.keywords_extracted or not job_description.keywords_extracted:
                raise ValueError("Keywords not extracted for resume or job description")
            
            # Get existing match score
            match_score = MatchScore.query.filter_by(
                resume_id=resume_id,
                job_description_id=job_description_id,
                user_id=user_id,
                is_active=True
            ).first()
            
            # Prepare data for AI analysis
            analysis_data = self._prepare_analysis_data(resume, job_description, match_score)
            
            # Generate suggestions using OpenAI or fallback
            if self.openai_available:
                suggestions = self._generate_openai_suggestions(analysis_data, suggestion_type)
            else:
                suggestions = self._generate_fallback_suggestions(analysis_data, suggestion_type)
            
            self.logger.info(f"Generated premium suggestions for resume {resume_id} vs JD {job_description_id}")
            
            return {
                'success': True,
                'suggestions': suggestions,
                'analysis_data': analysis_data,
                'suggestion_type': suggestion_type,
                'ai_powered': self.openai_available,
                'generated_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error generating premium suggestions: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _prepare_analysis_data(self, resume: Resume, job_description: JobDescription, 
                             match_score: Optional[MatchScore]) -> Dict:
        """
        Prepare comprehensive data for AI analysis
        """
        resume_keywords = resume.get_keywords()
        jd_keywords = job_description.get_keywords()
        
        # Calculate missing keywords
        missing_keywords = {}
        for category in ['technical_skills', 'soft_skills', 'other_keywords']:
            resume_set = set(kw.lower().strip() for kw in resume_keywords[category])
            jd_set = set(kw.lower().strip() for kw in jd_keywords[category])
            missing_keywords[category] = list(jd_set - resume_set)
        
        return {
            'resume': {
                'filename': resume.original_filename,
                'title': resume.title,
                'text_excerpt': resume.extracted_text[:1000] if resume.extracted_text else '',
                'keywords': resume_keywords,
                'keyword_count': resume.keyword_count
            },
            'job_description': {
                'title': job_description.title,
                'company': job_description.company_name,
                'text_excerpt': job_description.job_text[:1000] if job_description.job_text else '',
                'keywords': jd_keywords,
                'keyword_count': job_description.keyword_count
            },
            'missing_keywords': missing_keywords,
            'match_score': {
                'overall': match_score.overall_score if match_score else None,
                'technical': match_score.technical_score if match_score else None,
                'soft_skills': match_score.soft_skills_score if match_score else None,
                'other': match_score.other_keywords_score if match_score else None
            },
            'analysis_summary': {
                'total_missing': sum(len(keywords) for keywords in missing_keywords.values()),
                'missing_technical': len(missing_keywords['technical_skills']),
                'missing_soft_skills': len(missing_keywords['soft_skills']),
                'missing_other': len(missing_keywords['other_keywords'])
            }
        }
    
    def _generate_openai_suggestions(self, analysis_data: Dict, suggestion_type: str) -> Dict:
        """
        Generate suggestions using OpenAI API
        """
        try:
            # Create prompt based on suggestion type
            prompt = self._create_openai_prompt(analysis_data, suggestion_type)
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert resume optimization consultant with deep knowledge of ATS systems and hiring practices."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1500,
                temperature=0.7
            )
            
            # Parse response
            ai_response = response.choices[0].message.content
            
            # Structure the response
            return {
                'type': 'ai_powered',
                'content': self._parse_ai_response(ai_response),
                'raw_response': ai_response,
                'model_used': 'gpt-3.5-turbo',
                'tokens_used': response.usage.total_tokens if response.usage else None
            }
            
        except Exception as e:
            self.logger.error(f"OpenAI API error: {e}")
            # Fallback to local suggestions
            return self._generate_fallback_suggestions(analysis_data, suggestion_type)
    
    def _create_openai_prompt(self, analysis_data: Dict, suggestion_type: str) -> str:
        """
        Create optimized prompt for OpenAI based on analysis data
        """
        base_prompt = f"""
        Analyze this resume against the job description and provide specific, actionable suggestions:

        RESUME DETAILS:
        - Title: {analysis_data['resume']['title']}
        - Current Keywords: {analysis_data['resume']['keyword_count']}
        - Text Sample: {analysis_data['resume']['text_excerpt']}

        JOB DESCRIPTION:
        - Position: {analysis_data['job_description']['title']}
        - Company: {analysis_data['job_description']['company']}
        - Required Keywords: {analysis_data['job_description']['keyword_count']}
        - Text Sample: {analysis_data['job_description']['text_excerpt']}

        CURRENT MATCH ANALYSIS:
        - Overall Score: {analysis_data['match_score']['overall']}%
        - Missing Technical Skills: {analysis_data['missing_keywords']['technical_skills']}
        - Missing Soft Skills: {analysis_data['missing_keywords']['soft_skills']}
        - Missing Keywords: {analysis_data['missing_keywords']['other_keywords']}
        """
        
        if suggestion_type == 'comprehensive':
            prompt_suffix = """
            Provide comprehensive suggestions including:
            1. Specific keyword integration strategies
            2. Resume structure improvements
            3. ATS optimization tips
            4. Industry-specific recommendations
            5. Quantifiable achievement suggestions
            """
        elif suggestion_type == 'quick':
            prompt_suffix = """
            Provide quick, high-impact suggestions:
            1. Top 5 keywords to add immediately
            2. 3 most important sections to modify
            3. Quick wins for ATS compatibility
            """
        else:  # targeted
            prompt_suffix = """
            Provide targeted suggestions focused on:
            1. Closing the biggest skill gaps
            2. Addressing low match score areas
            3. Industry-specific optimizations
            """
        
        return base_prompt + prompt_suffix + "\n\nFormat your response as actionable bullet points with specific examples."
    
    def _parse_ai_response(self, ai_response: str) -> Dict:
        """
        Parse and structure AI response into organized suggestions
        """
        # Simple parsing - in production, you might want more sophisticated parsing
        lines = ai_response.split('\n')
        
        suggestions = {
            'summary': '',
            'keyword_recommendations': [],
            'structure_improvements': [],
            'ats_optimizations': [],
            'industry_specific': [],
            'quick_wins': []
        }
        
        current_section = 'summary'
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Detect section headers
            if 'keyword' in line.lower():
                current_section = 'keyword_recommendations'
            elif 'structure' in line.lower() or 'format' in line.lower():
                current_section = 'structure_improvements'
            elif 'ats' in line.lower():
                current_section = 'ats_optimizations'
            elif 'industry' in line.lower():
                current_section = 'industry_specific'
            elif 'quick' in line.lower() or 'immediate' in line.lower():
                current_section = 'quick_wins'
            
            # Add content to appropriate section
            if line.startswith('-') or line.startswith('•') or line.startswith('*'):
                suggestions[current_section].append(line[1:].strip())
            elif current_section == 'summary' and len(line) > 20:
                suggestions['summary'] += line + ' '
        
        return suggestions
    
    def _generate_fallback_suggestions(self, analysis_data: Dict, suggestion_type: str) -> Dict:
        """
        Generate suggestions using local logic when OpenAI is not available
        """
        template = self.fallback_templates.get(suggestion_type, self.fallback_templates['comprehensive'])
        
        # Customize template with actual data
        missing_tech = analysis_data['missing_keywords']['technical_skills'][:5]
        missing_soft = analysis_data['missing_keywords']['soft_skills'][:3]
        match_score = analysis_data['match_score']['overall'] or 0
        
        suggestions = {
            'type': 'fallback_logic',
            'content': {
                'summary': template['summary'].format(
                    match_score=match_score,
                    missing_count=analysis_data['analysis_summary']['total_missing']
                ),
                'keyword_recommendations': [
                    f"Add technical skill: {skill}" for skill in missing_tech
                ] + [
                    f"Highlight soft skill: {skill}" for skill in missing_soft
                ],
                'structure_improvements': template['structure_improvements'],
                'ats_optimizations': template['ats_optimizations'],
                'industry_specific': template['industry_specific'],
                'quick_wins': template['quick_wins']
            }
        }
        
        return suggestions
    
    def _get_comprehensive_template(self) -> Dict:
        return {
            'summary': 'Your resume has a {match_score}% match with {missing_count} missing keywords. Comprehensive optimization recommended.',
            'structure_improvements': [
                'Use bullet points for better ATS scanning',
                'Include a professional summary section',
                'Organize experience in reverse chronological order',
                'Use consistent formatting throughout'
            ],
            'ats_optimizations': [
                'Use standard section headers (Experience, Education, Skills)',
                'Avoid graphics, tables, and complex formatting',
                'Save as both PDF and Word formats',
                'Include keywords naturally in context'
            ],
            'industry_specific': [
                'Research industry-specific terminology',
                'Include relevant certifications and training',
                'Highlight measurable achievements',
                'Use action verbs appropriate for your field'
            ],
            'quick_wins': [
                'Add missing technical skills you possess',
                'Quantify your achievements with numbers',
                'Tailor your professional summary',
                'Include relevant keywords in job descriptions'
            ]
        }
    
    def _get_quick_template(self) -> Dict:
        return {
            'summary': 'Quick optimization for {match_score}% match score. Focus on high-impact changes.',
            'structure_improvements': [
                'Reorganize skills section to highlight relevant abilities',
                'Update professional summary with job-specific keywords'
            ],
            'ats_optimizations': [
                'Ensure keywords appear in multiple sections',
                'Use exact keyword phrases from job description'
            ],
            'industry_specific': [
                'Include industry buzzwords naturally',
                'Highlight relevant project experience'
            ],
            'quick_wins': [
                'Add top 5 missing technical skills',
                'Include 2-3 missing soft skills',
                'Update job titles to match industry standards'
            ]
        }
    
    def _get_targeted_template(self) -> Dict:
        return {
            'summary': 'Targeted optimization for {match_score}% match. Focus on closing specific skill gaps.',
            'structure_improvements': [
                'Create dedicated sections for missing skill categories',
                'Reorganize experience to highlight relevant projects'
            ],
            'ats_optimizations': [
                'Optimize for specific missing keywords',
                'Ensure keyword density is appropriate'
            ],
            'industry_specific': [
                'Research company-specific terminology',
                'Include relevant industry certifications'
            ],
            'quick_wins': [
                'Address lowest-scoring skill categories first',
                'Focus on high-priority missing keywords',
                'Tailor experience descriptions to job requirements'
            ]
        }
    
    def check_openai_status(self) -> Dict:
        """
        Check if OpenAI API is available and working
        """
        return {
            'available': self.openai_available,
            'api_key_configured': bool(os.getenv('OPENAI_API_KEY')),
            'library_installed': OPENAI_AVAILABLE
        }
