"""
US-07: Basic Suggestions Service
Identifies missing keywords and provides basic recommendations using local logic
"""

import logging
from typing import Dict, List, Set, Tuple
from models import db, Resume, JobDescription, MatchScore
from collections import Counter
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SuggestionsService:
    """
    Service for generating basic keyword suggestions and recommendations
    """
    
    def __init__(self):
        self.logger = logger
        
        # Priority keywords by category (local logic)
        self.priority_technical_skills = {
            'python', 'javascript', 'java', 'react', 'node.js', 'sql', 'postgresql', 
            'mongodb', 'docker', 'kubernetes', 'aws', 'azure', 'git', 'linux',
            'machine learning', 'data analysis', 'flask', 'django', 'fastapi',
            'html', 'css', 'typescript', 'angular', 'vue.js', 'express.js'
        }
        
        self.priority_soft_skills = {
            'communication', 'leadership', 'teamwork', 'problem-solving',
            'analytical thinking', 'creativity', 'adaptability', 'time management',
            'project management', 'collaboration', 'critical thinking'
        }
        
        # Common industry terms
        self.industry_keywords = {
            'agile', 'scrum', 'devops', 'ci/cd', 'testing', 'debugging',
            'optimization', 'scalability', 'security', 'performance',
            'architecture', 'design patterns', 'api', 'microservices'
        }
    
    def generate_basic_suggestions(self, resume_id: int, job_description_id: int, user_id: int) -> Dict:
        """
        Generate basic suggestions by identifying missing keywords and providing recommendations
        
        Args:
            resume_id: ID of the resume
            job_description_id: ID of the job description
            user_id: ID of the user (for security)
            
        Returns:
            Dict containing missing keywords and basic recommendations
        """
        try:
            # Get resume and job description
            resume = Resume.query.filter_by(
                id=resume_id,
                user_id=user_id,
                is_active=True
            ).first()
            
            job_description = JobDescription.query.filter_by(
                id=job_description_id,
                user_id=user_id,
                is_active=True
            ).first()
            
            if not resume:
                raise ValueError(f"Resume {resume_id} not found")
            
            if not job_description:
                raise ValueError(f"Job description {job_description_id} not found")
            
            if not resume.keywords_extracted:
                raise ValueError(f"Resume {resume_id} keywords not extracted yet")
            
            if not job_description.keywords_extracted:
                raise ValueError(f"Job description {job_description_id} keywords not extracted yet")
            
            # Get keywords
            resume_keywords = resume.get_keywords()
            jd_keywords = job_description.get_keywords()
            
            # Identify missing keywords by category
            missing_keywords = self._identify_missing_keywords(resume_keywords, jd_keywords)
            
            # Generate basic recommendations
            recommendations = self._generate_basic_recommendations(
                missing_keywords, resume_keywords, jd_keywords
            )
            
            # Calculate priority scores
            priority_analysis = self._analyze_keyword_priority(missing_keywords)
            
            # Get match score if available
            match_score = MatchScore.query.filter_by(
                resume_id=resume_id,
                job_description_id=job_description_id,
                user_id=user_id,
                is_active=True
            ).first()
            
            self.logger.info(f"Generated basic suggestions for resume {resume_id} vs JD {job_description_id}")
            
            return {
                'success': True,
                'missing_keywords': missing_keywords,
                'recommendations': recommendations,
                'priority_analysis': priority_analysis,
                'summary': {
                    'total_missing': sum(len(keywords) for keywords in missing_keywords.values()),
                    'high_priority_missing': len(priority_analysis['high_priority']),
                    'current_match_score': match_score.overall_score if match_score else None
                },
                'resume_info': {
                    'id': resume.id,
                    'filename': resume.original_filename,
                    'total_keywords': resume.keyword_count
                },
                'job_info': {
                    'id': job_description.id,
                    'title': job_description.title,
                    'company': job_description.company_name,
                    'total_keywords': job_description.keyword_count
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error generating basic suggestions: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _identify_missing_keywords(self, resume_keywords: Dict, jd_keywords: Dict) -> Dict:
        """
        Identify keywords present in job description but missing from resume
        """
        missing = {}
        
        for category in ['technical_skills', 'soft_skills', 'other_keywords']:
            resume_set = set(keyword.lower().strip() for keyword in resume_keywords[category])
            jd_set = set(keyword.lower().strip() for keyword in jd_keywords[category])
            
            missing_in_category = list(jd_set - resume_set)
            missing[category] = sorted(missing_in_category)
        
        return missing
    
    def _generate_basic_recommendations(self, missing_keywords: Dict, resume_keywords: Dict, jd_keywords: Dict) -> List[Dict]:
        """
        Generate basic recommendations based on missing keywords
        """
        recommendations = []
        
        # Technical skills recommendations
        if missing_keywords['technical_skills']:
            high_priority_tech = [
                kw for kw in missing_keywords['technical_skills'] 
                if kw in self.priority_technical_skills
            ]
            
            if high_priority_tech:
                recommendations.append({
                    'type': 'technical_skills',
                    'priority': 'high',
                    'title': 'Add High-Priority Technical Skills',
                    'description': f'Consider adding these in-demand technical skills: {", ".join(high_priority_tech[:5])}',
                    'keywords': high_priority_tech[:5],
                    'action': 'Add these skills to your resume if you have experience with them'
                })
            
            other_tech = [kw for kw in missing_keywords['technical_skills'] if kw not in high_priority_tech]
            if other_tech:
                recommendations.append({
                    'type': 'technical_skills',
                    'priority': 'medium',
                    'title': 'Additional Technical Skills',
                    'description': f'Job requires these technical skills: {", ".join(other_tech[:5])}',
                    'keywords': other_tech[:5],
                    'action': 'Consider learning these skills or highlighting related experience'
                })
        
        # Soft skills recommendations
        if missing_keywords['soft_skills']:
            priority_soft = [
                kw for kw in missing_keywords['soft_skills'] 
                if kw in self.priority_soft_skills
            ]
            
            if priority_soft:
                recommendations.append({
                    'type': 'soft_skills',
                    'priority': 'medium',
                    'title': 'Highlight Soft Skills',
                    'description': f'Emphasize these soft skills: {", ".join(priority_soft)}',
                    'keywords': priority_soft,
                    'action': 'Add examples demonstrating these soft skills in your experience section'
                })
        
        # Industry keywords recommendations
        if missing_keywords['other_keywords']:
            industry_terms = [
                kw for kw in missing_keywords['other_keywords'] 
                if kw in self.industry_keywords
            ]
            
            if industry_terms:
                recommendations.append({
                    'type': 'industry_keywords',
                    'priority': 'medium',
                    'title': 'Industry-Specific Terms',
                    'description': f'Include relevant industry terms: {", ".join(industry_terms[:5])}',
                    'keywords': industry_terms[:5],
                    'action': 'Incorporate these terms naturally into your job descriptions'
                })
        
        # General recommendations
        total_missing = sum(len(keywords) for keywords in missing_keywords.values())
        if total_missing > 10:
            recommendations.append({
                'type': 'general',
                'priority': 'high',
                'title': 'Significant Keyword Gap',
                'description': f'Your resume is missing {total_missing} keywords from the job description',
                'keywords': [],
                'action': 'Consider tailoring your resume more closely to this specific job posting'
            })
        elif total_missing > 5:
            recommendations.append({
                'type': 'general',
                'priority': 'medium',
                'title': 'Moderate Keyword Gap',
                'description': f'Adding {total_missing} relevant keywords could improve your match score',
                'keywords': [],
                'action': 'Focus on the high-priority missing keywords first'
            })
        else:
            recommendations.append({
                'type': 'general',
                'priority': 'low',
                'title': 'Good Keyword Coverage',
                'description': 'Your resume has good keyword coverage for this job',
                'keywords': [],
                'action': 'Fine-tune with the remaining missing keywords for optimal results'
            })
        
        return recommendations
    
    def _analyze_keyword_priority(self, missing_keywords: Dict) -> Dict:
        """
        Analyze and categorize missing keywords by priority
        """
        high_priority = []
        medium_priority = []
        low_priority = []
        
        # Technical skills priority
        for keyword in missing_keywords['technical_skills']:
            if keyword in self.priority_technical_skills:
                high_priority.append({
                    'keyword': keyword,
                    'category': 'technical_skills',
                    'reason': 'High-demand technical skill'
                })
            else:
                medium_priority.append({
                    'keyword': keyword,
                    'category': 'technical_skills',
                    'reason': 'Job-specific technical requirement'
                })
        
        # Soft skills priority
        for keyword in missing_keywords['soft_skills']:
            if keyword in self.priority_soft_skills:
                medium_priority.append({
                    'keyword': keyword,
                    'category': 'soft_skills',
                    'reason': 'Important soft skill'
                })
            else:
                low_priority.append({
                    'keyword': keyword,
                    'category': 'soft_skills',
                    'reason': 'Additional soft skill'
                })
        
        # Industry keywords priority
        for keyword in missing_keywords['other_keywords']:
            if keyword in self.industry_keywords:
                medium_priority.append({
                    'keyword': keyword,
                    'category': 'industry_keywords',
                    'reason': 'Industry-specific term'
                })
            else:
                low_priority.append({
                    'keyword': keyword,
                    'category': 'other_keywords',
                    'reason': 'General keyword'
                })
        
        return {
            'high_priority': high_priority,
            'medium_priority': medium_priority,
            'low_priority': low_priority,
            'total_by_priority': {
                'high': len(high_priority),
                'medium': len(medium_priority),
                'low': len(low_priority)
            }
        }
    
    def get_suggestion_history(self, user_id: int, limit: int = 10) -> List[Dict]:
        """
        Get recent suggestion requests for a user (based on match scores)
        """
        try:
            # Get recent match scores as proxy for suggestion history
            recent_matches = MatchScore.query.filter_by(
                user_id=user_id,
                is_active=True
            ).order_by(MatchScore.created_at.desc()).limit(limit).all()
            
            history = []
            for match in recent_matches:
                history.append({
                    'match_id': match.id,
                    'resume_title': match.resume.original_filename if match.resume else 'Unknown',
                    'job_title': match.job_description.title if match.job_description else 'Unknown',
                    'company_name': match.job_description.company_name if match.job_description else None,
                    'match_score': match.overall_score,
                    'created_at': match.created_at.isoformat(),
                    'can_generate_suggestions': True
                })
            
            return history
            
        except Exception as e:
            self.logger.error(f"Error fetching suggestion history: {e}")
            return []
