"""
US-07: Basic & Premium Suggestions Routes
API endpoints for generating keyword suggestions and recommendations
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, User, Resume, JobDescription, MatchScore
from services.suggestions_service import SuggestionsService
try:
    from services.premium_suggestions_service import PremiumSuggestionsService
    premium_service = PremiumSuggestionsService()
except ImportError:
    premium_service = None
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create blueprint for suggestions routes
suggestions_bp = Blueprint('suggestions', __name__, url_prefix='/api')

# Initialize services
suggestions_service = SuggestionsService()


@suggestions_bp.route('/basic_suggestions', methods=['POST'])
@jwt_required()
def generate_basic_suggestions():
    """
    Generate basic suggestions by identifying missing keywords
    
    Expected JSON:
    {
        "resume_id": 1,
        "job_description_id": 2
    }
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404
        
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        resume_id = data.get('resume_id')
        job_description_id = data.get('job_description_id')
        
        if not resume_id or not job_description_id:
            return jsonify({
                'success': False,
                'message': 'resume_id and job_description_id are required'
            }), 400
        
        # Validate that resume and job description belong to user
        resume = Resume.query.filter_by(
            id=resume_id,
            user_id=user.id,
            is_active=True
        ).first()
        
        job_description = JobDescription.query.filter_by(
            id=job_description_id,
            user_id=user.id,
            is_active=True
        ).first()
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found or access denied'
            }), 404
        
        if not job_description:
            return jsonify({
                'success': False,
                'message': 'Job description not found or access denied'
            }), 404
        
        # Check if keywords are extracted
        if not resume.keywords_extracted:
            return jsonify({
                'success': False,
                'message': 'Resume keywords not extracted yet. Please extract keywords first.'
            }), 400
        
        if not job_description.keywords_extracted:
            return jsonify({
                'success': False,
                'message': 'Job description keywords not extracted yet. Please extract keywords first.'
            }), 400
        
        # Generate basic suggestions
        suggestions_result = suggestions_service.generate_basic_suggestions(
            resume_id=resume_id,
            job_description_id=job_description_id,
            user_id=user.id
        )
        
        if not suggestions_result['success']:
            return jsonify({
                'success': False,
                'message': suggestions_result.get('error', 'Failed to generate suggestions')
            }), 500
        
        logger.info(f"Generated basic suggestions for user {user.id}, resume {resume_id}, JD {job_description_id}")
        
        return jsonify({
            'success': True,
            'message': 'Basic suggestions generated successfully',
            'suggestions': suggestions_result,
            'generated_at': datetime.utcnow().isoformat()
        }), 200
        
    except Exception as e:
        logger.error(f"Error in generate_basic_suggestions: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500


@suggestions_bp.route('/premium_suggestions', methods=['POST'])
@jwt_required()
def generate_premium_suggestions():
    """
    Generate premium AI-powered suggestions using OpenAI API
    
    Expected JSON:
    {
        "resume_id": 1,
        "job_description_id": 2,
        "suggestion_type": "comprehensive" // optional: "comprehensive", "quick", "targeted"
    }
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404
        
        # Check if user has premium access (you can implement premium user logic here)
        # For now, we'll allow all users to test
        # if not user.is_premium:
        #     return jsonify({
        #         'success': False,
        #         'message': 'Premium subscription required',
        #         'upgrade_required': True
        #     }), 403
        
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        resume_id = data.get('resume_id')
        job_description_id = data.get('job_description_id')
        suggestion_type = data.get('suggestion_type', 'comprehensive')
        
        if not resume_id or not job_description_id:
            return jsonify({
                'success': False,
                'message': 'resume_id and job_description_id are required'
            }), 400
        
        # Validate that resume and job description belong to user
        resume = Resume.query.filter_by(
            id=resume_id,
            user_id=user.id,
            is_active=True
        ).first()
        
        job_description = JobDescription.query.filter_by(
            id=job_description_id,
            user_id=user.id,
            is_active=True
        ).first()
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found or access denied'
            }), 404
        
        if not job_description:
            return jsonify({
                'success': False,
                'message': 'Job description not found or access denied'
            }), 404
        
        # Check if keywords are extracted
        if not resume.keywords_extracted:
            return jsonify({
                'success': False,
                'message': 'Resume keywords not extracted yet. Please extract keywords first.'
            }), 400
        
        if not job_description.keywords_extracted:
            return jsonify({
                'success': False,
                'message': 'Job description keywords not extracted yet. Please extract keywords first.'
            }), 400
        
        # Generate premium suggestions using OpenAI
        premium_result = premium_service.generate_premium_suggestions(
            resume_id=resume_id,
            job_description_id=job_description_id,
            user_id=user.id,
            suggestion_type=suggestion_type
        )
        
        if not premium_result['success']:
            return jsonify({
                'success': False,
                'message': premium_result.get('error', 'Failed to generate premium suggestions')
            }), 500
        
        logger.info(f"Generated premium suggestions for user {user.id}, resume {resume_id}, JD {job_description_id}")
        
        return jsonify({
            'success': True,
            'message': 'Premium suggestions generated successfully',
            'suggestions': premium_result,
            'generated_at': datetime.utcnow().isoformat(),
            'suggestion_type': suggestion_type
        }), 200
        
    except Exception as e:
        logger.error(f"Error in generate_premium_suggestions: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500


@suggestions_bp.route('/suggestion_history', methods=['GET'])
@jwt_required()
def get_suggestion_history():
    """
    Get user's suggestion history
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404
        
        # Get query parameters
        limit = request.args.get('limit', 10, type=int)
        limit = min(limit, 50)  # Cap at 50
        
        # Get suggestion history
        history = suggestions_service.get_suggestion_history(user.id, limit)
        
        return jsonify({
            'success': True,
            'suggestion_history': history,
            'total_count': len(history)
        }), 200
        
    except Exception as e:
        logger.error(f"Error in get_suggestion_history: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500


@suggestions_bp.route('/available_suggestions', methods=['GET'])
@jwt_required()
def get_available_suggestions():
    """
    Get available resume-JD combinations for generating suggestions
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404
        
        # Get resumes with extracted keywords
        resumes = Resume.query.filter_by(
            user_id=user.id,
            is_active=True,
            keywords_extracted=True
        ).all()
        
        # Get job descriptions with extracted keywords
        job_descriptions = JobDescription.query.filter_by(
            user_id=user.id,
            is_active=True,
            keywords_extracted=True
        ).all()
        
        resume_list = []
        for resume in resumes:
            resume_list.append({
                'id': resume.id,
                'filename': resume.original_filename,
                'title': resume.title,
                'keyword_count': resume.keyword_count,
                'upload_date': resume.created_at.isoformat()
            })
        
        jd_list = []
        for jd in job_descriptions:
            jd_list.append({
                'id': jd.id,
                'title': jd.title,
                'company_name': jd.company_name,
                'keyword_count': jd.keyword_count,
                'created_date': jd.created_at.isoformat()
            })
        
        return jsonify({
            'success': True,
            'resumes': resume_list,
            'job_descriptions': jd_list,
            'total_combinations': len(resume_list) * len(jd_list)
        }), 200
        
    except Exception as e:
        logger.error(f"Error in get_available_suggestions: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500
