/**
 * US-07: Suggestions JavaScript
 * Handles basic and premium suggestions functionality
 */

class SuggestionsManager {
    constructor() {
        this.token = localStorage.getItem('dr_resume_token');
        this.resumes = [];
        this.jobDescriptions = [];
        this.currentSuggestions = null;
        this.init();
    }

    init() {
        if (!this.token) {
            window.location.href = '/login';
            return;
        }

        this.setupEventListeners();
        this.loadAvailableData();
        this.loadSuggestionHistory();
    }

    setupEventListeners() {
        // Basic suggestions button
        document.getElementById('basicSuggestionsBtn').addEventListener('click', () => {
            this.generateBasicSuggestions();
        });

        // Premium suggestions button
        document.getElementById('premiumSuggestionsBtn').addEventListener('click', () => {
            this.generatePremiumSuggestions();
        });

        // Resume and job selection change
        document.getElementById('resumeSelect').addEventListener('change', () => {
            this.validateForm();
        });

        document.getElementById('jobSelect').addEventListener('change', () => {
            this.validateForm();
        });

        // Logout button
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.logout();
        });

        // Premium upgrade button (if shown)
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-premium-upgrade')) {
                this.showPremiumUpgrade();
            }
        });
    }

    async loadAvailableData() {
        try {
            const response = await fetch('/api/available_suggestions', {
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.resumes = data.resumes || [];
                this.jobDescriptions = data.job_descriptions || [];
                this.populateSelectors();
            } else {
                console.error('Failed to load available data');
                this.showError('Failed to load available resumes and job descriptions');
            }
        } catch (error) {
            console.error('Error loading available data:', error);
            this.showError('Error loading available data');
        }
    }

    populateSelectors() {
        // Populate resume selector
        const resumeSelect = document.getElementById('resumeSelect');
        resumeSelect.innerHTML = '<option value="">Select a resume...</option>';

        if (this.resumes.length === 0) {
            resumeSelect.innerHTML = '<option value="">No resumes with keywords available</option>';
        } else {
            this.resumes.forEach(resume => {
                const option = document.createElement('option');
                option.value = resume.id;
                option.textContent = `${resume.filename} (${resume.keyword_count} keywords)`;
                resumeSelect.appendChild(option);
            });
        }

        // Populate job description selector
        const jobSelect = document.getElementById('jobSelect');
        jobSelect.innerHTML = '<option value="">Select a job description...</option>';

        if (this.jobDescriptions.length === 0) {
            jobSelect.innerHTML = '<option value="">No job descriptions with keywords available</option>';
        } else {
            this.jobDescriptions.forEach(jd => {
                const option = document.createElement('option');
                option.value = jd.id;
                option.textContent = `${jd.title} - ${jd.company_name || 'No Company'} (${jd.keyword_count} keywords)`;
                jobSelect.appendChild(option);
            });
        }

        this.validateForm();
    }

    validateForm() {
        const resumeId = document.getElementById('resumeSelect').value;
        const jobId = document.getElementById('jobSelect').value;
        const basicBtn = document.getElementById('basicSuggestionsBtn');
        const premiumBtn = document.getElementById('premiumSuggestionsBtn');

        const isValid = resumeId && jobId;
        basicBtn.disabled = !isValid;
        premiumBtn.disabled = !isValid;
    }

    async generateBasicSuggestions() {
        const resumeId = document.getElementById('resumeSelect').value;
        const jobId = document.getElementById('jobSelect').value;

        if (!resumeId || !jobId) {
            this.showError('Please select both a resume and job description');
            return;
        }

        // Show loading
        this.showLoading('Analyzing keywords and generating suggestions...');
        this.hideSuggestionSections();

        try {
            const response = await fetch('/api/basic_suggestions', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    resume_id: parseInt(resumeId),
                    job_description_id: parseInt(jobId)
                })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                this.displayBasicSuggestions(data.suggestions);
                this.showSuccess('Basic suggestions generated successfully!');
            } else {
                this.showError(data.message || 'Failed to generate basic suggestions');
            }
        } catch (error) {
            console.error('Error generating basic suggestions:', error);
            this.showError('Error generating basic suggestions');
        } finally {
            this.hideLoading();
        }
    }

    async generatePremiumSuggestions() {
        const resumeId = document.getElementById('resumeSelect').value;
        const jobId = document.getElementById('jobSelect').value;

        if (!resumeId || !jobId) {
            this.showError('Please select both a resume and job description');
            return;
        }

        // Show loading
        this.showLoading('Generating AI-powered premium suggestions...');
        this.hideSuggestionSections();

        try {
            const response = await fetch('/api/premium_suggestions', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    resume_id: parseInt(resumeId),
                    job_description_id: parseInt(jobId),
                    suggestion_type: 'comprehensive'
                })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                this.displayPremiumSuggestions(data.suggestions);
                this.showSuccess('Premium AI suggestions generated successfully!');
            } else if (response.status === 403) {
                this.showPremiumUpgradeCTA();
            } else {
                this.showError(data.message || 'Failed to generate premium suggestions');
            }
        } catch (error) {
            console.error('Error generating premium suggestions:', error);
            this.showError('Error generating premium suggestions');
        } finally {
            this.hideLoading();
        }
    }

    displayBasicSuggestions(suggestions) {
        // Display missing keywords
        this.displayMissingKeywords(suggestions.missing_keywords);
        
        // Display recommendations
        this.displayRecommendations(suggestions.recommendations);
        
        // Display priority analysis
        this.displayPriorityAnalysis(suggestions.priority_analysis);
        
        // Show the basic suggestions section
        document.getElementById('basicSuggestionsSection').style.display = 'block';
    }

    displayMissingKeywords(missingKeywords) {
        // Technical skills
        const technicalContainer = document.getElementById('missingTechnicalSkills');
        this.renderKeywordPills(technicalContainer, missingKeywords.technical_skills, 'technical');
        
        // Soft skills
        const softContainer = document.getElementById('missingSoftSkills');
        this.renderKeywordPills(softContainer, missingKeywords.soft_skills, 'soft');
        
        // Other keywords
        const otherContainer = document.getElementById('missingOtherKeywords');
        this.renderKeywordPills(otherContainer, missingKeywords.other_keywords, 'other');
    }

    renderKeywordPills(container, keywords, type) {
        container.innerHTML = '';
        
        if (keywords.length === 0) {
            container.innerHTML = '<div class="empty-keywords">No missing keywords in this category</div>';
            return;
        }
        
        keywords.forEach(keyword => {
            const pill = document.createElement('div');
            pill.className = `keyword-pill ${type}`;
            pill.innerHTML = `
                <span>${keyword}</span>
                <span class="pill-icon">+</span>
            `;
            container.appendChild(pill);
        });
    }

    displayRecommendations(recommendations) {
        const container = document.getElementById('basicRecommendations');
        container.innerHTML = '';
        
        recommendations.forEach(rec => {
            const item = document.createElement('div');
            item.className = 'recommendation-item';
            item.innerHTML = `
                <div class="recommendation-header">
                    <div>
                        <div class="recommendation-title">${rec.title}</div>
                        <div class="recommendation-priority ${rec.priority}">${rec.priority}</div>
                    </div>
                </div>
                <div class="recommendation-description">${rec.description}</div>
                <div class="recommendation-action">💡 ${rec.action}</div>
            `;
            container.appendChild(item);
        });
    }

    displayPriorityAnalysis(priorityAnalysis) {
        const container = document.getElementById('priorityBreakdown');
        container.innerHTML = `
            <div class="priority-item">
                <div class="priority-count high">${priorityAnalysis.total_by_priority.high}</div>
                <div class="priority-label">High Priority</div>
            </div>
            <div class="priority-item">
                <div class="priority-count medium">${priorityAnalysis.total_by_priority.medium}</div>
                <div class="priority-label">Medium Priority</div>
            </div>
            <div class="priority-item">
                <div class="priority-count low">${priorityAnalysis.total_by_priority.low}</div>
                <div class="priority-label">Low Priority</div>
            </div>
        `;
    }

    displayPremiumSuggestions(suggestions) {
        const content = suggestions.suggestions.content;
        
        // AI Summary
        document.getElementById('aiSummary').textContent = content.summary || 'AI analysis completed successfully.';
        
        // Keyword recommendations
        this.renderAIRecommendations('aiKeywordRecommendations', content.keyword_recommendations);
        
        // Structure improvements
        this.renderAIRecommendations('aiStructureImprovements', content.structure_improvements);
        
        // ATS optimizations
        this.renderAIRecommendations('aiAtsOptimizations', content.ats_optimizations);
        
        // Quick wins
        this.renderAIRecommendations('aiQuickWins', content.quick_wins);
        
        // Show premium content
        document.getElementById('premiumContent').style.display = 'block';
        document.getElementById('premiumUpgradeCTA').style.display = 'none';
        document.getElementById('premiumSuggestionsSection').style.display = 'block';
    }

    renderAIRecommendations(containerId, recommendations) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';
        
        if (!recommendations || recommendations.length === 0) {
            container.innerHTML = '<div class="ai-recommendation-item">No specific recommendations in this category.</div>';
            return;
        }
        
        recommendations.forEach(rec => {
            const item = document.createElement('div');
            item.className = 'ai-recommendation-item';
            item.textContent = rec;
            container.appendChild(item);
        });
    }

    showPremiumUpgradeCTA() {
        document.getElementById('premiumContent').style.display = 'none';
        document.getElementById('premiumUpgradeCTA').style.display = 'block';
        document.getElementById('premiumSuggestionsSection').style.display = 'block';
    }

    async loadSuggestionHistory() {
        try {
            const response = await fetch('/api/suggestion_history?limit=5', {
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.displaySuggestionHistory(data.suggestion_history || []);
            }
        } catch (error) {
            console.error('Error loading suggestion history:', error);
        }
    }

    displaySuggestionHistory(history) {
        const container = document.getElementById('suggestionHistoryContainer');
        
        if (history.length === 0) {
            container.innerHTML = '<div class="loading-text">No suggestion history available</div>';
            return;
        }

        container.innerHTML = history.map(item => `
            <div class="history-item">
                <div class="history-header">
                    <div>
                        <div class="history-title">${item.resume_title} vs ${item.job_title}</div>
                        <div class="history-meta">
                            ${item.company_name ? `${item.company_name} • ` : ''}
                            ${new Date(item.created_at).toLocaleDateString()}
                        </div>
                    </div>
                    <div class="history-score">
                        <div class="history-score-value">${item.match_score}%</div>
                        <div class="history-score-label">Match Score</div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    showLoading(message = 'Loading...') {
        document.getElementById('loadingText').textContent = message;
        document.getElementById('loadingSection').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loadingSection').style.display = 'none';
    }

    hideSuggestionSections() {
        document.getElementById('basicSuggestionsSection').style.display = 'none';
        document.getElementById('premiumSuggestionsSection').style.display = 'none';
    }

    showPremiumUpgrade() {
        alert('Premium upgrade functionality would be implemented here.\n\nFeatures include:\n• AI-powered suggestions\n• Advanced optimization tips\n• Industry-specific recommendations\n• Priority support');
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        setTimeout(() => {
            errorDiv.style.display = 'none';
        }, 5000);
    }

    showSuccess(message) {
        const successDiv = document.getElementById('successMessage');
        successDiv.textContent = message;
        successDiv.style.display = 'block';
        setTimeout(() => {
            successDiv.style.display = 'none';
        }, 3000);
    }

    logout() {
        localStorage.removeItem('access_token');
        localStorage.removeItem('user_info');
        window.location.href = '/login';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SuggestionsManager();
});
