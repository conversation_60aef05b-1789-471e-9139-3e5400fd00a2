# Dr. Resume US-07 Requirements: Suggestions System with OpenAI Integration
# Building on US-01 through US-06

# Core Flask Framework
Flask==2.3.3
Flask-CORS==4.0.0

# Database & ORM
SQLAlchemy==2.0.21
Flask-SQLAlchemy==3.0.5

# Authentication & Security
Flask-JWT-Extended==4.5.3
Werkzeug==2.3.7
bcrypt==4.0.1

# File Processing (from US-03)
PyPDF2==3.0.1
python-docx==0.8.11

# NLP & Matching Libraries (from US-05 + new for US-06)
nltk==3.8.1
scikit-learn==1.3.0
numpy==1.24.3

# Similarity & Matching Algorithms (NEW for US-06)
fuzzywuzzy==0.18.0
python-Levenshtein==0.21.1

# Environment & Configuration
python-dotenv==1.0.0

# AI & Premium Features (NEW for US-07.1)
openai>=1.0.0

# Development & Testing
pytest==7.4.2
pytest-flask==1.2.0
