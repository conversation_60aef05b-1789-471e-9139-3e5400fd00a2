/* Dr. Resume US-07 Styles - Suggestions & Premium Features */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 100%;
    max-width: 800px;
}

.header {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
}

.header h1 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.header .subtitle {
    font-size: 16px;
    opacity: 0.9;
    font-weight: 400;
}

.content {
    padding: 40px 30px;
}

.welcome-text {
    text-align: center;
    color: #6b7280;
    margin-bottom: 30px;
    font-size: 16px;
    line-height: 1.5;
}

.btn {
    width: 100%;
    padding: 16px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    margin-bottom: 12px;
}

.btn-primary {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(124, 58, 237, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #7c3aed;
    border: 2px solid #7c3aed;
}

.btn-secondary:hover {
    background: #7c3aed;
    color: white;
    transform: translateY(-2px);
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #374151;
    font-weight: 500;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.2s ease;
    background: #f9fafb;
}

.form-control:focus {
    outline: none;
    border-color: #7c3aed;
    background: white;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
}

/* File Upload Styles */
.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    background: #f9fafb;
    transition: all 0.2s ease;
    cursor: pointer;
    margin-bottom: 20px;
}

.upload-area:hover {
    border-color: #7c3aed;
    background: #f3f4f6;
}

.upload-area.dragover {
    border-color: #7c3aed;
    background: #ede9fe;
}

.upload-icon {
    font-size: 48px;
    color: #9ca3af;
    margin-bottom: 16px;
}

.upload-text {
    color: #6b7280;
    margin-bottom: 8px;
}

.upload-subtext {
    color: #9ca3af;
    font-size: 14px;
}

.file-input {
    display: none;
}

.file-info {
    background: #f3f4f6;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    display: none;
}

.file-name {
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
}

.file-size {
    color: #6b7280;
    font-size: 14px;
}

/* Progress Bar */
.progress-container {
    margin: 20px 0;
    display: none;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    margin-top: 8px;
    color: #6b7280;
    font-size: 14px;
}

/* Textarea Styles */
.textarea-container {
    position: relative;
}

.form-textarea {
    width: 100%;
    min-height: 300px;
    padding: 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    font-family: inherit;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.2s ease;
    background: #f9fafb;
}

.form-textarea:focus {
    outline: none;
    border-color: #7c3aed;
    background: white;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
}

.character-counter {
    position: absolute;
    bottom: 12px;
    right: 16px;
    background: rgba(255, 255, 255, 0.9);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #6b7280;
    border: 1px solid #e5e7eb;
}

.character-counter.warning {
    color: #f59e0b;
    border-color: #f59e0b;
}

.character-counter.error {
    color: #ef4444;
    border-color: #ef4444;
}

/* Alert Messages */
.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
}

.alert-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.alert-warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fcd34d;
}

/* Dashboard Styles */
.dashboard-container {
    max-width: 1200px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.dashboard-header {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    color: white;
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-nav {
    display: flex;
    gap: 20px;
    align-items: center;
}

.nav-item {
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.2);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.dashboard-content {
    padding: 40px;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.dashboard-card {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    transition: all 0.2s ease;
}

.dashboard-card:hover {
    border-color: #7c3aed;
    transform: translateY(-2px);
}

.card-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.card-description {
    color: #6b7280;
    margin-bottom: 20px;
    line-height: 1.5;
}

.card-button {
    background: #7c3aed;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.card-button:hover {
    background: #6d28d9;
}

/* Job Description List Styles */
.jd-list {
    margin-top: 30px;
}

.jd-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    transition: all 0.2s ease;
}

.jd-item:hover {
    border-color: #7c3aed;
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.1);
}

.jd-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.jd-title {
    font-weight: 600;
    color: #1f2937;
    font-size: 18px;
    margin-bottom: 4px;
}

.jd-company {
    color: #7c3aed;
    font-weight: 500;
    margin-bottom: 8px;
}

.jd-meta {
    color: #6b7280;
    font-size: 14px;
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.jd-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.btn-small {
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-view {
    background: #3b82f6;
    color: white;
}

.btn-view:hover {
    background: #2563eb;
}

.btn-edit {
    background: #10b981;
    color: white;
}

.btn-edit:hover {
    background: #059669;
}

.btn-delete {
    background: #ef4444;
    color: white;
}

.btn-delete:hover {
    background: #dc2626;
}

/* Loading State */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .dashboard-nav {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .jd-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .jd-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .container {
        max-width: none;
        margin: 10px;
    }
}

@media (max-width: 480px) {
    .container, .dashboard-container {
        margin: 10px;
        max-width: none;
    }
    
    .header, .dashboard-content {
        padding: 30px 20px;
    }
    
    .content {
        padding: 30px 20px;
    }
    
    .header h1 {
        font-size: 24px;
    }
    
    .form-textarea {
        min-height: 250px;
    }
}

/* Keyword Analysis Styles (NEW for US-05) */
.keyword-section {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
}

.keyword-section h3 {
    color: #1f2937;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.keyword-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
}

.keyword-tag {
    background: #f3f4f6;
    color: #374151;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.keyword-tag.technical {
    background: #dbeafe;
    color: #1e40af;
    border-color: #93c5fd;
}

.keyword-tag.soft {
    background: #dcfce7;
    color: #166534;
    border-color: #86efac;
}

.keyword-tag.other {
    background: #fef3c7;
    color: #92400e;
    border-color: #fcd34d;
}

.keyword-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.keyword-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.keyword-stat {
    text-align: center;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
}

.keyword-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #7c3aed;
    margin-bottom: 4px;
}

.keyword-stat-label {
    color: #6b7280;
    font-size: 14px;
}

.parse-button {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.parse-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.parse-button:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.parse-all-section {
    background: #f8fafc;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    text-align: center;
}

.parse-all-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 16px;
}

.keyword-empty {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 24px;
}

.keyword-analysis-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.2s ease;
}

.keyword-analysis-card:hover {
    border-color: #7c3aed;
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.1);
}

.keyword-analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.keyword-analysis-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
}

.keyword-analysis-meta {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 8px;
}

.keyword-analysis-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.keyword-count-badge {
    background: #7c3aed;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.no-keywords {
    background: #f3f4f6;
    color: #6b7280;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

/* US-06: Matching Score Styles */
.main-content {
    padding: 40px;
    max-width: 1200px;
    margin: 0 auto;
}

.match-calculator {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
}

.match-calculator h2 {
    color: #1f2937;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.calculator-form {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 20px;
    align-items: end;
}

.form-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    background: white;
    transition: border-color 0.2s ease;
}

.form-select:focus {
    outline: none;
    border-color: #7c3aed;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
}

.loading-section {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 12px;
    margin-bottom: 30px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #7c3aed;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

.match-result {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
}

.score-display {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 40px;
    align-items: center;
    margin-bottom: 30px;
}

.overall-score {
    text-align: center;
}

.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(#7c3aed 0deg, #a855f7 180deg, #e5e7eb 180deg);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 0 auto;
}

.score-circle::before {
    content: '';
    position: absolute;
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 50%;
}

.score-value {
    font-size: 24px;
    font-weight: bold;
    color: #1f2937;
    z-index: 1;
}

.score-label {
    font-size: 12px;
    color: #6b7280;
    z-index: 1;
}

.detailed-scores {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.score-item {
    display: grid;
    grid-template-columns: 150px 1fr auto;
    gap: 16px;
    align-items: center;
}

.score-category {
    font-weight: 500;
    color: #374151;
}

.progress-bar {
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
    transition: width 0.5s ease;
    border-radius: 4px;
}

.score-percentage {
    font-weight: 600;
    color: #1f2937;
    min-width: 50px;
    text-align: right;
}

.match-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    background: #f8fafc;
    padding: 20px;
    border-radius: 8px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detail-label {
    color: #6b7280;
    font-size: 14px;
}

.detail-value {
    font-weight: 600;
    color: #1f2937;
}

.match-history {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 30px;
}

.match-history h2 {
    color: #1f2937;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.history-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.history-item {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s ease;
}

.history-item:hover {
    border-color: #7c3aed;
    box-shadow: 0 2px 8px rgba(124, 58, 237, 0.1);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.history-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
}

.history-meta {
    color: #6b7280;
    font-size: 14px;
}

.history-score {
    text-align: right;
}

.history-score-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 4px;
}

.history-score-value.excellent {
    color: #10b981;
}

.history-score-value.good {
    color: #3b82f6;
}

.history-score-value.fair {
    color: #f59e0b;
}

.history-score-value.poor {
    color: #ef4444;
}

.history-score-label {
    font-size: 12px;
    color: #6b7280;
}

.loading-text {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 40px;
}

.error-message, .success-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 20px;
    border-radius: 8px;
    font-weight: 500;
    z-index: 1000;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.error-message {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.success-message {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

/* Navigation Styles */
.nav {
    display: flex;
    gap: 16px;
    align-items: center;
    margin-top: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* Responsive Design for Matching */
@media (max-width: 768px) {
    .calculator-form {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .score-display {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .score-item {
        grid-template-columns: 1fr;
        gap: 8px;
        text-align: center;
    }

    .match-details {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .detail-item {
        flex-direction: column;
        gap: 4px;
        text-align: center;
    }

    .history-header {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .nav {
        gap: 8px;
    }

    .nav-link {
        padding: 6px 12px;
        font-size: 13px;
    }
}

/* US-07: Suggestions Page Styles */
.suggestion-calculator {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
}

.suggestion-calculator h2 {
    color: #1f2937;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.suggestion-buttons {
    display: flex;
    gap: 16px;
    margin-top: 20px;
}

.btn-premium {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-premium:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
}

.btn-premium:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.suggestions-section {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
}

.premium-section {
    border: 2px solid #8b5cf6;
    background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f3f4f6;
}

.suggestion-type-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.suggestion-type-badge.basic {
    background: #dbeafe;
    color: #1e40af;
}

.suggestion-type-badge.premium {
    background: #ede9fe;
    color: #7c3aed;
}

/* Missing Keywords Styles */
.missing-keywords-container {
    margin-bottom: 30px;
}

.keyword-categories {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.keyword-category h4 {
    color: #374151;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.keyword-pills {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    min-height: 40px;
    padding: 8px;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px dashed #d1d5db;
}

.keyword-pill {
    background: #ef4444;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
}

.keyword-pill:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.keyword-pill.technical {
    background: #3b82f6;
}

.keyword-pill.soft {
    background: #10b981;
}

.keyword-pill.other {
    background: #f59e0b;
}

.empty-keywords {
    color: #6b7280;
    font-style: italic;
    text-align: center;
    padding: 16px;
}

/* Recommendations Styles */
.recommendations-container {
    margin-bottom: 30px;
}

.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.recommendation-item {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s ease;
}

.recommendation-item:hover {
    border-color: #7c3aed;
    box-shadow: 0 2px 8px rgba(124, 58, 237, 0.1);
}

.recommendation-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.recommendation-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
}

.recommendation-priority {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.recommendation-priority.high {
    background: #fee2e2;
    color: #991b1b;
}

.recommendation-priority.medium {
    background: #fef3c7;
    color: #92400e;
}

.recommendation-priority.low {
    background: #d1fae5;
    color: #065f46;
}

.recommendation-description {
    color: #6b7280;
    margin-bottom: 12px;
    line-height: 1.5;
}

.recommendation-action {
    color: #7c3aed;
    font-weight: 500;
    font-size: 14px;
}

/* Priority Analysis Styles */
.priority-analysis {
    background: #f8fafc;
    border-radius: 8px;
    padding: 20px;
}

.priority-breakdown {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.priority-item {
    text-align: center;
    padding: 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.priority-count {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 4px;
}

.priority-count.high {
    color: #ef4444;
}

.priority-count.medium {
    color: #f59e0b;
}

.priority-count.low {
    color: #10b981;
}

.priority-label {
    color: #6b7280;
    font-size: 14px;
    text-transform: uppercase;
    font-weight: 500;
}

/* Premium Section Styles */
.premium-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.ai-summary {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #0ea5e9;
}

.ai-summary-content {
    color: #0f172a;
    line-height: 1.6;
    font-size: 16px;
}

.ai-recommendations {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #22c55e;
}

.structure-improvements {
    background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #eab308;
}

.ats-optimizations {
    background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #ec4899;
}

.quick-wins {
    background: linear-gradient(135deg, #f5f3ff 0%, #ede9fe 100%);
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #8b5cf6;
}

.ai-recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 12px;
}

.ai-recommendation-item {
    background: rgba(255, 255, 255, 0.7);
    padding: 12px 16px;
    border-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #374151;
    line-height: 1.5;
}

/* Premium Upgrade CTA */
.premium-upgrade-cta {
    text-align: center;
    padding: 40px;
    background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
    border-radius: 12px;
    border: 2px dashed #8b5cf6;
}

.upgrade-content h3 {
    color: #7c3aed;
    margin-bottom: 16px;
}

.upgrade-content p {
    color: #6b7280;
    margin-bottom: 24px;
    line-height: 1.6;
}

.premium-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 24px;
}

.feature {
    color: #7c3aed;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-premium-upgrade {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-premium-upgrade:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
}

/* Responsive Design for Suggestions */
@media (max-width: 768px) {
    .suggestion-buttons {
        flex-direction: column;
        gap: 12px;
    }

    .keyword-categories {
        gap: 16px;
    }

    .priority-breakdown {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .premium-features {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .section-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }
}

/* US-09: Scan History Styles */
.scan-history-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.scan-history-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.score-badge {
    font-weight: bold;
    text-align: center;
    border-radius: 20px;
    padding: 4px 12px;
    font-size: 14px;
    display: inline-block;
}

.score-excellent {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.score-good {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #bfdbfe;
}

.score-fair {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.score-poor {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

/* Pagination Styles */
#historyPagination button {
    transition: all 0.2s ease;
}

#historyPagination button:hover:not(:disabled) {
    background: #7c3aed !important;
    color: white !important;
    transform: translateY(-1px);
}

#historyPagination button:disabled {
    cursor: not-allowed !important;
    opacity: 0.5 !important;
}

/* Filter Dropdown */
#scoreFilter {
    transition: border-color 0.2s ease;
}

#scoreFilter:focus {
    outline: none;
    border-color: #7c3aed;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
}

/* Loading Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner {
    animation: spin 1s linear infinite;
    display: inline-block;
}

/* Responsive Design for Scan History */
@media (max-width: 768px) {
    .scan-history-card {
        padding: 15px !important;
    }

    .scan-history-card > div:first-child {
        flex-direction: column !important;
        gap: 15px !important;
    }

    .scan-history-card .score-badge {
        align-self: flex-start;
    }

    .scan-history-card > div:last-child {
        flex-direction: column !important;
        gap: 8px !important;
    }

    .scan-history-card button {
        width: 100% !important;
        text-align: center !important;
    }
}
