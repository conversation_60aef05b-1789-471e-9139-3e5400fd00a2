// Dr. Resume US-09 Dashboard JavaScript with Scan History

// Global variables for pagination
let currentPage = 1;
let currentFilter = '';

document.addEventListener('DOMContentLoaded', function() {
    console.log('🩺 Dr. Resume Dashboard Loaded (US-09)');

    // Check authentication
    checkAuthentication();

    // Load user info
    loadUserInfo();

    // Load user job descriptions
    loadUserJobDescriptions();

    // Load user resumes
    loadUserResumes();

    // Load scan history
    loadScanHistory();

    // Load dashboard statistics
    loadDashboardStats();
});

async function checkAuthentication() {
    const token = localStorage.getItem('dr_resume_token');
    
    if (!token) {
        console.log('❌ No token found, redirecting to login');
        window.location.href = '/login';
        return;
    }
    
    try {
        // Verify token by calling protected endpoint
        const response = await fetch('/api/profile', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('Token verification failed');
        }
        
        const result = await response.json();
        
        if (!result.success) {
            throw new Error('Invalid token response');
        }
        
        console.log('✅ Token verified successfully');
        
        // Update user info in localStorage if needed
        localStorage.setItem('dr_resume_user', JSON.stringify(result.user));
        
    } catch (error) {
        console.error('Authentication error:', error);
        
        // Clear invalid tokens and redirect
        localStorage.removeItem('dr_resume_token');
        localStorage.removeItem('dr_resume_refresh_token');
        localStorage.removeItem('dr_resume_user');
        
        alert('Your session has expired. Please log in again.');
        window.location.href = '/login';
    }
}

function loadUserInfo() {
    try {
        const userStr = localStorage.getItem('dr_resume_user');
        
        if (userStr) {
            const user = JSON.parse(userStr);
            
            // Update welcome message
            const welcomeMessage = document.getElementById('welcomeMessage');
            const userName = document.getElementById('userName');
            
            if (welcomeMessage) {
                welcomeMessage.textContent = `Welcome, ${user.first_name}`;
            }
            
            if (userName) {
                userName.textContent = user.first_name;
            }
            
            // Update logout button
            const logoutBtn = document.querySelector('.logout-btn');
            if (logoutBtn) {
                logoutBtn.textContent = '🚪 Logout';
                logoutBtn.onclick = logout;
            }
            
            console.log('👤 User info loaded:', user.first_name, user.email);
        }
        
    } catch (error) {
        console.error('Error loading user info:', error);
    }
}

async function loadUserJobDescriptions() {
    try {
        const token = localStorage.getItem('dr_resume_token');
        
        const response = await fetch('/api/job_descriptions', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('Failed to fetch job descriptions');
        }
        
        const result = await response.json();
        
        if (result.success) {
            displayJobDescriptions(result.job_descriptions);
            updateJDStats(result.count);
        } else {
            console.error('Error fetching job descriptions:', result.message);
        }
        
    } catch (error) {
        console.error('Error loading job descriptions:', error);
    }
}

function displayJobDescriptions(jobDescriptions) {
    const jdListSection = document.getElementById('jdListSection');
    const jdList = document.getElementById('jdList');
    const jdCardDescription = document.getElementById('jdCardDescription');
    
    if (jobDescriptions.length === 0) {
        jdListSection.style.display = 'none';
        jdCardDescription.textContent = 'No job descriptions yet. Add one to get started!';
        return;
    }
    
    // Update card description
    jdCardDescription.textContent = `${jobDescriptions.length} job description${jobDescriptions.length > 1 ? 's' : ''} saved. Add more or manage existing ones.`;
    
    // Show job description list
    jdListSection.style.display = 'block';
    
    // Generate job description list HTML
    const jdHTML = jobDescriptions.map(jd => `
        <div class="jd-item">
            <div class="jd-header">
                <div>
                    <div class="jd-title">${jd.title}</div>
                    ${jd.company_name ? `<div class="jd-company">${jd.company_name}</div>` : ''}
                    <div class="jd-meta">
                        <span>${jd.word_count.toLocaleString()} words</span>
                        <span>${jd.character_count.toLocaleString()} characters</span>
                        <span>${formatDate(jd.created_at)}</span>
                    </div>
                </div>
                <div class="jd-actions">
                    <button class="btn-small btn-view" onclick="viewJobDescription(${jd.id})">
                        👁️ View
                    </button>
                    <button class="btn-small btn-edit" onclick="editJobDescription(${jd.id})">
                        ✏️ Edit
                    </button>
                    <button class="btn-small btn-delete" onclick="deleteJobDescription(${jd.id}, '${jd.title}')">
                        🗑️ Delete
                    </button>
                </div>
            </div>
        </div>
    `).join('');
    
    jdList.innerHTML = jdHTML;
}

function updateJDStats(count) {
    const jdCount = document.getElementById('jdCount');
    if (jdCount) {
        jdCount.textContent = count;
    }
}

async function viewJobDescription(jdId) {
    try {
        const token = localStorage.getItem('dr_resume_token');
        
        const response = await fetch(`/api/job_descriptions/${jdId}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('Failed to fetch job description details');
        }
        
        const result = await response.json();
        
        if (result.success) {
            showJobDescriptionModal(result.job_description);
        } else {
            alert('Error loading job description details: ' + result.message);
        }
        
    } catch (error) {
        console.error('Error viewing job description:', error);
        alert('Error loading job description details. Please try again.');
    }
}

async function deleteJobDescription(jdId, jdTitle) {
    if (!confirm(`Are you sure you want to delete "${jdTitle}"? This action cannot be undone.`)) {
        return;
    }
    
    try {
        const token = localStorage.getItem('dr_resume_token');
        
        const response = await fetch(`/api/job_descriptions/${jdId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('Failed to delete job description');
        }
        
        const result = await response.json();
        
        if (result.success) {
            alert('Job description deleted successfully');
            // Reload job descriptions
            loadUserJobDescriptions();
        } else {
            alert('Error deleting job description: ' + result.message);
        }
        
    } catch (error) {
        console.error('Error deleting job description:', error);
        alert('Error deleting job description. Please try again.');
    }
}

function editJobDescription(jdId) {
    // For now, redirect to add page with edit functionality
    // In a full implementation, you'd pass the ID and load existing data
    alert('Edit functionality will be enhanced in future versions. For now, you can create a new job description.');
}

function showJobDescriptionModal(jd) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    `;
    
    const content = document.createElement('div');
    content.style.cssText = `
        background: white;
        border-radius: 12px;
        padding: 30px;
        max-width: 800px;
        max-height: 80vh;
        overflow-y: auto;
        margin: 20px;
    `;
    
    content.innerHTML = `
        <h3 style="margin-bottom: 20px; color: #1f2937;">${jd.title}</h3>
        ${jd.company_name ? `<div style="margin-bottom: 20px; color: #7c3aed; font-weight: 500;">${jd.company_name}</div>` : ''}
        <div style="margin-bottom: 20px; color: #6b7280;">
            <strong>Words:</strong> ${jd.word_count.toLocaleString()}<br>
            <strong>Characters:</strong> ${jd.character_count.toLocaleString()}<br>
            <strong>Created:</strong> ${formatDate(jd.created_at)}
        </div>
        <div style="margin-bottom: 20px;">
            <strong>Job Description:</strong>
            <div style="background: #f3f4f6; padding: 15px; border-radius: 8px; max-height: 400px; overflow-y: auto; white-space: pre-wrap; font-family: inherit; font-size: 14px; line-height: 1.5; margin-top: 10px;">
${jd.job_text}
            </div>
        </div>
        <button onclick="this.closest('div').parentElement.remove()" style="background: #7c3aed; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">
            Close
        </button>
    `;
    
    modal.appendChild(content);
    document.body.appendChild(modal);
    
    // Close on background click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

async function logout() {
    try {
        const token = localStorage.getItem('dr_resume_token');
        
        if (token) {
            await fetch('/api/logout', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
        }
        
    } catch (error) {
        console.error('Logout API error:', error);
    } finally {
        // Clear all stored data
        localStorage.removeItem('dr_resume_token');
        localStorage.removeItem('dr_resume_refresh_token');
        localStorage.removeItem('dr_resume_user');
        
        console.log('🚪 Logged out successfully');
        
        // Redirect to landing page
        window.location.href = '/';
    }
}

// Navigation functions
function addJobDescription() {
    window.location.href = '/add-job-description';
}

function uploadResume() {
    window.location.href = '/upload';
}

function analyzeKeywords() {
    window.location.href = '/keywords';
}

async function loadUserResumes() {
    try {
        const token = localStorage.getItem('dr_resume_token');

        const response = await fetch('/api/resumes', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to fetch resumes');
        }

        const result = await response.json();

        if (result.success) {
            displayResumes(result.resumes);
            updateResumeStats(result.count);
        } else {
            console.error('Error fetching resumes:', result.message);
        }

    } catch (error) {
        console.error('Error loading resumes:', error);
    }
}

function displayResumes(resumes) {
    const resumeListSection = document.getElementById('resumeListSection');
    const resumeList = document.getElementById('resumeList');
    const resumeCardDescription = document.getElementById('resumeCardDescription');

    if (resumes.length === 0) {
        resumeListSection.style.display = 'none';
        resumeCardDescription.textContent = 'No resumes uploaded yet. Upload one to get started!';
        return;
    }

    // Update card description
    resumeCardDescription.textContent = `${resumes.length} resume${resumes.length > 1 ? 's' : ''} uploaded. Upload more or manage existing ones.`;

    // Show resume list
    resumeListSection.style.display = 'block';

    // Generate resume list HTML
    const resumeHTML = resumes.map(resume => `
        <div class="jd-item">
            <div class="jd-header">
                <div>
                    <div class="jd-title">${resume.title}</div>
                    <div class="jd-meta">
                        <span>${resume.file_size_formatted}</span>
                        <span>${resume.file_type.toUpperCase()}</span>
                        <span style="color: ${getStatusColor(resume.upload_status)}">${getStatusText(resume.upload_status)}</span>
                        <span>${formatDate(resume.created_at)}</span>
                    </div>
                </div>
                <div class="jd-actions">
                    <button class="btn-small btn-view" onclick="viewResume(${resume.id})">
                        👁️ View
                    </button>
                    <button class="btn-small btn-delete" onclick="deleteResume(${resume.id}, '${resume.title}')">
                        🗑️ Delete
                    </button>
                </div>
            </div>
        </div>
    `).join('');

    resumeList.innerHTML = resumeHTML;
}

function updateResumeStats(count) {
    const resumeCount = document.getElementById('resumeCount');
    if (resumeCount) {
        resumeCount.textContent = count;
    }
}

async function viewResume(resumeId) {
    try {
        const token = localStorage.getItem('dr_resume_token');

        const response = await fetch(`/api/resumes/${resumeId}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to fetch resume details');
        }

        const result = await response.json();

        if (result.success) {
            showResumeModal(result.resume);
        } else {
            alert('Error loading resume details: ' + result.message);
        }

    } catch (error) {
        console.error('Error viewing resume:', error);
        alert('Error loading resume details. Please try again.');
    }
}

async function deleteResume(resumeId, resumeTitle) {
    if (!confirm(`Are you sure you want to delete "${resumeTitle}"? This action cannot be undone.`)) {
        return;
    }

    try {
        const token = localStorage.getItem('dr_resume_token');

        const response = await fetch(`/api/resumes/${resumeId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to delete resume');
        }

        const result = await response.json();

        if (result.success) {
            alert('Resume deleted successfully');
            // Reload resumes
            loadUserResumes();
        } else {
            alert('Error deleting resume: ' + result.message);
        }

    } catch (error) {
        console.error('Error deleting resume:', error);
        alert('Error deleting resume. Please try again.');
    }
}

function showResumeModal(resume) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
        background: white;
        border-radius: 12px;
        padding: 30px;
        max-width: 800px;
        max-height: 80vh;
        overflow-y: auto;
        margin: 20px;
    `;

    content.innerHTML = `
        <h3 style="margin-bottom: 20px; color: #1f2937;">${resume.title}</h3>
        <div style="margin-bottom: 20px; color: #6b7280;">
            <strong>File:</strong> ${resume.original_filename}<br>
            <strong>Size:</strong> ${resume.file_size_formatted}<br>
            <strong>Type:</strong> ${resume.file_type.toUpperCase()}<br>
            <strong>Status:</strong> <span style="color: ${getStatusColor(resume.upload_status)}">${getStatusText(resume.upload_status)}</span><br>
            <strong>Uploaded:</strong> ${formatDate(resume.created_at)}
        </div>
        ${resume.extracted_text ? `
            <div style="margin-bottom: 20px;">
                <strong>Extracted Text:</strong>
                <div style="background: #f3f4f6; padding: 15px; border-radius: 8px; max-height: 300px; overflow-y: auto; white-space: pre-wrap; font-family: monospace; font-size: 14px;">
${resume.extracted_text}
                </div>
            </div>
        ` : ''}
        <button onclick="this.closest('div').parentElement.remove()" style="background: #7c3aed; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">
            Close
        </button>
    `;

    modal.appendChild(content);
    document.body.appendChild(modal);

    // Close on background click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

function getStatusColor(status) {
    switch (status) {
        case 'completed': return '#10b981';
        case 'processing': return '#f59e0b';
        case 'failed': return '#ef4444';
        default: return '#6b7280';
    }
}

function getStatusText(status) {
    switch (status) {
        case 'completed': return 'Processed';
        case 'processing': return 'Processing';
        case 'failed': return 'Failed';
        default: return 'Unknown';
    }
}

// US-09: Scan History Functions
async function loadScanHistory(page = 1, filter = '') {
    const token = localStorage.getItem('dr_resume_token');

    if (!token) {
        console.log('❌ No token found');
        return;
    }

    try {
        showHistoryLoading();

        const params = new URLSearchParams({
            page: page,
            per_page: 5,
            sort_by: 'created_at',
            sort_order: 'desc'
        });

        if (filter) {
            params.append('filter_score', filter);
        }

        const response = await fetch(`/api/history?${params}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            displayScanHistory(result.scan_history, result.pagination);
            currentPage = result.pagination.page;
            currentFilter = filter;
        } else {
            throw new Error(result.message || 'Failed to load scan history');
        }

    } catch (error) {
        console.error('❌ Error loading scan history:', error);
        showHistoryError('Failed to load scan history. Please try again.');
    }
}

async function loadDashboardStats() {
    const token = localStorage.getItem('dr_resume_token');

    if (!token) {
        return;
    }

    try {
        const response = await fetch('/api/dashboard_stats', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            updateDashboardStats(result.stats);
        }

    } catch (error) {
        console.error('❌ Error loading dashboard stats:', error);
    }
}

function displayScanHistory(scanHistory, pagination) {
    const container = document.getElementById('scanHistoryContainer');
    const listElement = document.getElementById('scanHistoryList');
    const emptyElement = document.getElementById('scanHistoryEmpty');
    const loadingElement = document.getElementById('scanHistoryLoading');

    // Hide loading
    loadingElement.style.display = 'none';

    if (!scanHistory || scanHistory.length === 0) {
        emptyElement.style.display = 'block';
        listElement.style.display = 'none';
        hidePagination();
        return;
    }

    emptyElement.style.display = 'none';
    listElement.style.display = 'block';

    // Create scan history cards
    listElement.innerHTML = scanHistory.map(scan => `
        <div class="scan-history-card" style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 15px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <div style="display: flex; justify-content: between; align-items: flex-start; margin-bottom: 15px;">
                <div style="flex: 1;">
                    <h4 style="margin: 0 0 5px 0; color: #1f2937; font-size: 16px;">
                        📄 ${scan.resume.title || 'Untitled Resume'}
                    </h4>
                    <p style="margin: 0 0 5px 0; color: #6b7280; font-size: 14px;">
                        📋 ${scan.job_description.title} at ${scan.job_description.company_name}
                    </p>
                    <p style="margin: 0; color: #9ca3af; font-size: 12px;">
                        📅 ${formatDate(scan.created_at)}
                    </p>
                </div>
                <div style="text-align: right;">
                    <div class="score-badge ${getScoreClass(scan.score_category)}" style="display: inline-block; padding: 4px 12px; border-radius: 20px; font-weight: bold; font-size: 14px; margin-bottom: 8px;">
                        ${scan.match_score}%
                    </div>
                    <div style="font-size: 12px; color: #6b7280;">
                        ${scan.matched_keywords}/${scan.total_jd_keywords} keywords
                    </div>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-bottom: 15px;">
                <div style="text-align: center; padding: 8px; background: #f9fafb; border-radius: 6px;">
                    <div style="font-weight: bold; color: #7c3aed;">${scan.technical_score}%</div>
                    <div style="font-size: 12px; color: #6b7280;">Technical</div>
                </div>
                <div style="text-align: center; padding: 8px; background: #f9fafb; border-radius: 6px;">
                    <div style="font-weight: bold; color: #7c3aed;">${scan.soft_skills_score}%</div>
                    <div style="font-size: 12px; color: #6b7280;">Soft Skills</div>
                </div>
                <div style="text-align: center; padding: 8px; background: #f9fafb; border-radius: 6px;">
                    <div style="font-weight: bold; color: #7c3aed;">${scan.other_keywords_score}%</div>
                    <div style="font-size: 12px; color: #6b7280;">Other</div>
                </div>
            </div>

            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                <button onclick="viewSuggestions(${scan.resume.id}, ${scan.job_description.id})"
                        style="padding: 6px 12px; background: #7c3aed; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                    💡 View Suggestions
                </button>
                <button onclick="generatePremiumSuggestions(${scan.resume.id}, ${scan.job_description.id})"
                        style="padding: 6px 12px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                    ⭐ Premium AI
                </button>
            </div>
        </div>
    `).join('');

    // Update pagination
    updatePagination(pagination);
}

function updateDashboardStats(stats) {
    // Update the stats display in the dashboard
    const resumeCountElement = document.getElementById('resumeCount');
    const jdCountElement = document.getElementById('jdCount');

    if (resumeCountElement) {
        resumeCountElement.textContent = stats.total_resumes;
    }

    if (jdCountElement) {
        jdCountElement.textContent = stats.total_job_descriptions;
    }

    // Update other stats if elements exist
    const elements = document.querySelectorAll('[data-stat]');
    elements.forEach(element => {
        const statName = element.getAttribute('data-stat');
        if (stats[statName] !== undefined) {
            element.textContent = stats[statName];
        }
    });
}

function getScoreClass(category) {
    switch (category) {
        case 'excellent': return 'score-excellent';
        case 'good': return 'score-good';
        case 'fair': return 'score-fair';
        case 'poor': return 'score-poor';
        default: return 'score-fair';
    }
}

function showHistoryLoading() {
    document.getElementById('scanHistoryLoading').style.display = 'block';
    document.getElementById('scanHistoryEmpty').style.display = 'none';
    document.getElementById('scanHistoryList').style.display = 'none';
}

function showHistoryError(message) {
    const container = document.getElementById('scanHistoryContainer');
    container.innerHTML = `
        <div style="text-align: center; padding: 40px; color: #ef4444;">
            <div style="font-size: 48px; margin-bottom: 20px;">❌</div>
            <h4 style="margin: 0 0 10px 0;">Error Loading History</h4>
            <p style="margin: 0;">${message}</p>
            <button onclick="loadScanHistory()" style="margin-top: 15px; padding: 8px 16px; background: #7c3aed; color: white; border: none; border-radius: 6px; cursor: pointer;">
                🔄 Try Again
            </button>
        </div>
    `;
}

function updatePagination(pagination) {
    const paginationElement = document.getElementById('historyPagination');
    const prevBtn = document.getElementById('prevPageBtn');
    const nextBtn = document.getElementById('nextPageBtn');
    const pageInfo = document.getElementById('pageInfo');

    if (pagination.total_pages <= 1) {
        paginationElement.style.display = 'none';
        return;
    }

    paginationElement.style.display = 'block';
    prevBtn.disabled = !pagination.has_prev;
    nextBtn.disabled = !pagination.has_next;
    pageInfo.textContent = `Page ${pagination.page} of ${pagination.total_pages}`;

    // Update button styles
    prevBtn.style.opacity = pagination.has_prev ? '1' : '0.5';
    nextBtn.style.opacity = pagination.has_next ? '1' : '0.5';
    prevBtn.style.cursor = pagination.has_prev ? 'pointer' : 'not-allowed';
    nextBtn.style.cursor = pagination.has_next ? 'pointer' : 'not-allowed';
}

function hidePagination() {
    document.getElementById('historyPagination').style.display = 'none';
}

// Event handlers for scan history
function filterHistory() {
    const filter = document.getElementById('scoreFilter').value;
    currentPage = 1; // Reset to first page when filtering
    loadScanHistory(currentPage, filter);
}

function refreshHistory() {
    loadScanHistory(currentPage, currentFilter);
}

function changePage(direction) {
    const newPage = currentPage + direction;
    if (newPage >= 1) {
        loadScanHistory(newPage, currentFilter);
    }
}

function viewSuggestions(resumeId, jobDescriptionId) {
    // Navigate to suggestions page with parameters
    window.location.href = `/suggestions?resume_id=${resumeId}&job_description_id=${jobDescriptionId}`;
}

function generatePremiumSuggestions(resumeId, jobDescriptionId) {
    // Navigate to suggestions page with premium flag
    window.location.href = `/suggestions?resume_id=${resumeId}&job_description_id=${jobDescriptionId}&premium=true`;
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
}
