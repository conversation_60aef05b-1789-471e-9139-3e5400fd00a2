// Dr. Resume US-05 Keywords Analysis JavaScript

document.addEventListener('DOMContentLoaded', function() {
    console.log('🩺 Dr. Resume Keywords Analysis Page Loaded (US-05)');
    
    // Check authentication
    checkAuthentication();
    
    // Load user info
    loadUserInfo();
    
    // Load keyword statistics
    loadKeywordStats();
    
    // Load resume keywords
    loadResumeKeywords();
    
    // Load job description keywords
    loadJDKeywords();
});

async function checkAuthentication() {
    const token = localStorage.getItem('dr_resume_token');
    
    if (!token) {
        console.log('❌ No token found, redirecting to login');
        window.location.href = '/login';
        return;
    }
    
    try {
        // Verify token by calling protected endpoint
        const response = await fetch('/api/profile', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('Token verification failed');
        }
        
        const result = await response.json();
        
        if (!result.success) {
            throw new Error('Invalid token response');
        }
        
        console.log('✅ Token verified successfully');
        
        // Update user info in localStorage if needed
        localStorage.setItem('dr_resume_user', JSON.stringify(result.user));
        
    } catch (error) {
        console.error('Authentication error:', error);
        
        // Clear invalid tokens and redirect
        localStorage.removeItem('dr_resume_token');
        localStorage.removeItem('dr_resume_refresh_token');
        localStorage.removeItem('dr_resume_user');
        
        alert('Your session has expired. Please log in again.');
        window.location.href = '/login';
    }
}

function loadUserInfo() {
    try {
        const userStr = localStorage.getItem('dr_resume_user');
        
        if (userStr) {
            const user = JSON.parse(userStr);
            
            // Update welcome message
            const welcomeMessage = document.getElementById('welcomeMessage');
            
            if (welcomeMessage) {
                welcomeMessage.textContent = `Welcome, ${user.first_name}`;
            }
            
            // Update logout button
            const logoutBtn = document.querySelector('.logout-btn');
            if (logoutBtn) {
                logoutBtn.textContent = '🚪 Logout';
                logoutBtn.onclick = logout;
            }
            
            console.log('👤 User info loaded:', user.first_name, user.email);
        }
        
    } catch (error) {
        console.error('Error loading user info:', error);
    }
}

async function loadKeywordStats() {
    try {
        const token = localStorage.getItem('dr_resume_token');
        
        const response = await fetch('/api/keywords/stats', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('Failed to fetch keyword stats');
        }
        
        const result = await response.json();
        
        if (result.success) {
            updateKeywordStats(result.stats);
        } else {
            console.error('Error fetching keyword stats:', result.message);
        }
        
    } catch (error) {
        console.error('Error loading keyword stats:', error);
    }
}

function updateKeywordStats(stats) {
    document.getElementById('totalResumes').textContent = stats.resumes.total;
    document.getElementById('processedResumes').textContent = stats.resumes.processed;
    document.getElementById('totalJDs').textContent = stats.job_descriptions.total;
    document.getElementById('processedJDs').textContent = stats.job_descriptions.processed;
    
    // Update button states
    const parseAllResumesBtn = document.getElementById('parseAllResumesBtn');
    const parseAllJDsBtn = document.getElementById('parseAllJDsBtn');
    
    if (stats.resumes.pending === 0) {
        parseAllResumesBtn.textContent = '✅ All Resumes Processed';
        parseAllResumesBtn.disabled = true;
    }
    
    if (stats.job_descriptions.pending === 0) {
        parseAllJDsBtn.textContent = '✅ All Job Descriptions Processed';
        parseAllJDsBtn.disabled = true;
    }
}

async function loadResumeKeywords() {
    try {
        const token = localStorage.getItem('dr_resume_token');
        
        const response = await fetch('/api/resumes', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('Failed to fetch resumes');
        }
        
        const result = await response.json();
        
        if (result.success) {
            displayResumeKeywords(result.resumes);
        } else {
            console.error('Error fetching resumes:', result.message);
        }
        
    } catch (error) {
        console.error('Error loading resume keywords:', error);
        document.getElementById('resumeKeywordsContainer').innerHTML = 
            '<div class="keyword-empty">Error loading resume keywords</div>';
    }
}

async function loadJDKeywords() {
    try {
        const token = localStorage.getItem('dr_resume_token');
        
        const response = await fetch('/api/job_descriptions', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('Failed to fetch job descriptions');
        }
        
        const result = await response.json();
        
        if (result.success) {
            displayJDKeywords(result.job_descriptions);
        } else {
            console.error('Error fetching job descriptions:', result.message);
        }
        
    } catch (error) {
        console.error('Error loading job description keywords:', error);
        document.getElementById('jdKeywordsContainer').innerHTML = 
            '<div class="keyword-empty">Error loading job description keywords</div>';
    }
}

function displayResumeKeywords(resumes) {
    const container = document.getElementById('resumeKeywordsContainer');
    
    if (resumes.length === 0) {
        container.innerHTML = '<div class="keyword-empty">No resumes found. Upload some resumes first!</div>';
        return;
    }
    
    const resumeHTML = resumes.map(resume => `
        <div class="keyword-analysis-card">
            <div class="keyword-analysis-header">
                <div>
                    <div class="keyword-analysis-title">${resume.title}</div>
                    <div class="keyword-analysis-meta">
                        ${resume.file_type.toUpperCase()} • ${resume.file_size_formatted} • 
                        ${formatDate(resume.created_at)}
                    </div>
                </div>
                <div class="keyword-analysis-actions">
                    ${resume.keywords_extracted ? 
                        `<span class="keyword-count-badge">${resume.keyword_count} keywords</span>` :
                        `<span class="no-keywords">No keywords</span>`
                    }
                    <button class="parse-button" onclick="parseResumeKeywords(${resume.id})" 
                            ${!resume.has_extracted_text ? 'disabled' : ''}>
                        🔍 ${resume.keywords_extracted ? 'Re-parse' : 'Parse'}
                    </button>
                </div>
            </div>
            ${resume.keywords_extracted ? `
                <div id="resumeKeywords${resume.id}">
                    <div class="keyword-empty">Loading keywords...</div>
                </div>
            ` : ''}
        </div>
    `).join('');
    
    container.innerHTML = resumeHTML;
    
    // Load keywords for processed resumes
    resumes.forEach(resume => {
        if (resume.keywords_extracted) {
            loadSpecificResumeKeywords(resume.id);
        }
    });
}

function displayJDKeywords(jds) {
    const container = document.getElementById('jdKeywordsContainer');
    
    if (jds.length === 0) {
        container.innerHTML = '<div class="keyword-empty">No job descriptions found. Add some job descriptions first!</div>';
        return;
    }
    
    const jdHTML = jds.map(jd => `
        <div class="keyword-analysis-card">
            <div class="keyword-analysis-header">
                <div>
                    <div class="keyword-analysis-title">${jd.title}</div>
                    <div class="keyword-analysis-meta">
                        ${jd.company_name ? jd.company_name + ' • ' : ''}
                        ${jd.word_count} words • ${formatDate(jd.created_at)}
                    </div>
                </div>
                <div class="keyword-analysis-actions">
                    ${jd.keywords_extracted ? 
                        `<span class="keyword-count-badge">${jd.keyword_count} keywords</span>` :
                        `<span class="no-keywords">No keywords</span>`
                    }
                    <button class="parse-button" onclick="parseJDKeywords(${jd.id})">
                        🔍 ${jd.keywords_extracted ? 'Re-parse' : 'Parse'}
                    </button>
                </div>
            </div>
            ${jd.keywords_extracted ? `
                <div id="jdKeywords${jd.id}">
                    <div class="keyword-empty">Loading keywords...</div>
                </div>
            ` : ''}
        </div>
    `).join('');
    
    container.innerHTML = jdHTML;
    
    // Load keywords for processed JDs
    jds.forEach(jd => {
        if (jd.keywords_extracted) {
            loadSpecificJDKeywords(jd.id);
        }
    });
}

async function loadSpecificResumeKeywords(resumeId) {
    try {
        const token = localStorage.getItem('dr_resume_token');

        const response = await fetch(`/api/keywords/resume/${resumeId}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to fetch resume keywords');
        }

        const result = await response.json();

        if (result.success && result.resume.keywords) {
            displayKeywords(`resumeKeywords${resumeId}`, result.resume.keywords);
        }

    } catch (error) {
        console.error('Error loading specific resume keywords:', error);
    }
}

async function loadSpecificJDKeywords(jdId) {
    try {
        const token = localStorage.getItem('dr_resume_token');

        const response = await fetch(`/api/keywords/job_description/${jdId}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to fetch JD keywords');
        }

        const result = await response.json();

        if (result.success && result.job_description.keywords) {
            displayKeywords(`jdKeywords${jdId}`, result.job_description.keywords);
        }

    } catch (error) {
        console.error('Error loading specific JD keywords:', error);
    }
}

function displayKeywords(containerId, keywords) {
    const container = document.getElementById(containerId);

    if (!container) return;

    let html = '';

    // Technical Skills
    if (keywords.technical_skills && keywords.technical_skills.length > 0) {
        html += `
            <div style="margin-bottom: 16px;">
                <h4 style="color: #1e40af; margin-bottom: 8px;">💻 Technical Skills</h4>
                <div class="keyword-tags">
                    ${keywords.technical_skills.map(skill =>
                        `<span class="keyword-tag technical">${skill}</span>`
                    ).join('')}
                </div>
            </div>
        `;
    }

    // Soft Skills
    if (keywords.soft_skills && keywords.soft_skills.length > 0) {
        html += `
            <div style="margin-bottom: 16px;">
                <h4 style="color: #166534; margin-bottom: 8px;">🤝 Soft Skills</h4>
                <div class="keyword-tags">
                    ${keywords.soft_skills.map(skill =>
                        `<span class="keyword-tag soft">${skill}</span>`
                    ).join('')}
                </div>
            </div>
        `;
    }

    // Other Keywords
    if (keywords.other_keywords && keywords.other_keywords.length > 0) {
        html += `
            <div style="margin-bottom: 16px;">
                <h4 style="color: #92400e; margin-bottom: 8px;">🔍 Other Keywords</h4>
                <div class="keyword-tags">
                    ${keywords.other_keywords.map(keyword =>
                        `<span class="keyword-tag other">${keyword}</span>`
                    ).join('')}
                </div>
            </div>
        `;
    }

    if (!html) {
        html = '<div class="keyword-empty">No keywords extracted</div>';
    }

    container.innerHTML = html;
}

async function parseResumeKeywords(resumeId) {
    try {
        const token = localStorage.getItem('dr_resume_token');

        showAlert('info', 'Extracting keywords from resume...');

        const response = await fetch(`/api/parse_resume_keywords/${resumeId}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to parse resume keywords');
        }

        const result = await response.json();

        if (result.success) {
            showAlert('success', result.message);

            // Reload the page to show updated keywords
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert('error', result.message);
        }

    } catch (error) {
        console.error('Error parsing resume keywords:', error);
        showAlert('error', 'Error extracting keywords. Please try again.');
    }
}

async function parseJDKeywords(jdId) {
    try {
        const token = localStorage.getItem('dr_resume_token');

        showAlert('info', 'Extracting keywords from job description...');

        const response = await fetch(`/api/parse_jd_keywords/${jdId}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to parse JD keywords');
        }

        const result = await response.json();

        if (result.success) {
            showAlert('success', result.message);

            // Reload the page to show updated keywords
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert('error', result.message);
        }

    } catch (error) {
        console.error('Error parsing JD keywords:', error);
        showAlert('error', 'Error extracting keywords. Please try again.');
    }
}

async function parseAllResumes() {
    try {
        const token = localStorage.getItem('dr_resume_token');
        const button = document.getElementById('parseAllResumesBtn');

        // Disable button and show loading
        button.disabled = true;
        button.textContent = 'Processing...';

        showAlert('info', 'Processing all resumes for keyword extraction...');

        const response = await fetch('/api/parse_all_resume_keywords', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to parse all resume keywords');
        }

        const result = await response.json();

        if (result.success) {
            showAlert('success', result.message);

            // Reload the page to show updated keywords
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showAlert('error', result.message);
            button.disabled = false;
            button.textContent = '📋 Parse All Resumes';
        }

    } catch (error) {
        console.error('Error parsing all resume keywords:', error);
        showAlert('error', 'Error processing resumes. Please try again.');

        const button = document.getElementById('parseAllResumesBtn');
        button.disabled = false;
        button.textContent = '📋 Parse All Resumes';
    }
}

async function parseAllJDs() {
    try {
        const token = localStorage.getItem('dr_resume_token');
        const button = document.getElementById('parseAllJDsBtn');

        // Disable button and show loading
        button.disabled = true;
        button.textContent = 'Processing...';

        showAlert('info', 'Processing all job descriptions for keyword extraction...');

        const response = await fetch('/api/parse_all_jd_keywords', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Failed to parse all JD keywords');
        }

        const result = await response.json();

        if (result.success) {
            showAlert('success', result.message);

            // Reload the page to show updated keywords
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showAlert('error', result.message);
            button.disabled = false;
            button.textContent = '📄 Parse All Job Descriptions';
        }

    } catch (error) {
        console.error('Error parsing all JD keywords:', error);
        showAlert('error', 'Error processing job descriptions. Please try again.');

        const button = document.getElementById('parseAllJDsBtn');
        button.disabled = false;
        button.textContent = '📄 Parse All Job Descriptions';
    }
}

async function logout() {
    try {
        const token = localStorage.getItem('dr_resume_token');

        if (token) {
            await fetch('/api/logout', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
        }

    } catch (error) {
        console.error('Logout API error:', error);
    } finally {
        // Clear all stored data
        localStorage.removeItem('dr_resume_token');
        localStorage.removeItem('dr_resume_refresh_token');
        localStorage.removeItem('dr_resume_user');

        console.log('🚪 Logged out successfully');

        // Redirect to landing page
        window.location.href = '/';
    }
}

// UI Helper functions
function showAlert(type, message) {
    const alertContainer = document.getElementById('alertContainer');

    const alertHTML = `
        <div class="alert alert-${type}">
            <strong>${message}</strong>
        </div>
    `;

    alertContainer.innerHTML = alertHTML;

    // Scroll to top to show alert
    window.scrollTo({ top: 0, behavior: 'smooth' });

    // Auto-hide info alerts after 3 seconds
    if (type === 'info') {
        setTimeout(() => {
            alertContainer.innerHTML = '';
        }, 3000);
    }
}

function clearAlerts() {
    document.getElementById('alertContainer').innerHTML = '';
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
}
