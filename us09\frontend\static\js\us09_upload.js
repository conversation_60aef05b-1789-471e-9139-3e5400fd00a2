// Dr. Resume US-04 Upload JavaScript

document.addEventListener('DOMContentLoaded', function() {
    console.log('🩺 Dr. Resume Upload Page Loaded (US-04)');
    
    // Check authentication
    checkAuthentication();
    
    // Initialize upload functionality
    initializeUpload();
});

function checkAuthentication() {
    const token = localStorage.getItem('dr_resume_token');
    
    if (!token) {
        console.log('❌ No token found, redirecting to login');
        window.location.href = '/login';
        return;
    }
    
    // Verify token (simplified check)
    console.log('✅ Token found, user authenticated');
}

function initializeUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('resumeFile');
    const uploadForm = document.getElementById('uploadForm');
    const uploadBtn = document.getElementById('uploadBtn');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const progressContainer = document.getElementById('progressContainer');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    const alertContainer = document.getElementById('alertContainer');
    
    let selectedFile = null;
    
    // Click to upload
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });
    
    // Drag and drop functionality
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelection(files[0]);
        }
    });
    
    // File input change
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelection(e.target.files[0]);
        }
    });
    
    // Form submission
    uploadForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        if (!selectedFile) {
            showAlert('error', 'Please select a file to upload');
            return;
        }
        
        await uploadFile();
    });
    
    function handleFileSelection(file) {
        // Validate file type
        const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        const allowedExtensions = ['pdf', 'doc', 'docx'];
        
        const fileExtension = file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
            showAlert('error', 'Invalid file type. Please upload PDF, DOC, or DOCX files only.');
            return;
        }
        
        // Validate file size (16MB limit)
        const maxSize = 16 * 1024 * 1024; // 16MB
        if (file.size > maxSize) {
            showAlert('error', 'File too large. Maximum size is 16MB.');
            return;
        }
        
        selectedFile = file;
        
        // Show file info
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        fileInfo.style.display = 'block';
        
        // Enable upload button
        uploadBtn.disabled = false;
        uploadBtn.textContent = '📤 Upload Resume';
        
        // Clear any previous alerts
        clearAlerts();
        
        console.log('📄 File selected:', file.name, formatFileSize(file.size));
    }
    
    async function uploadFile() {
        const token = localStorage.getItem('dr_resume_token');
        
        if (!token) {
            showAlert('error', 'Authentication required. Please log in again.');
            window.location.href = '/login';
            return;
        }
        
        // Show progress
        setUploading(true);
        
        try {
            const formData = new FormData();
            formData.append('resume', selectedFile);
            
            const title = document.getElementById('resumeTitle').value.trim();
            if (title) {
                formData.append('title', title);
            }
            
            const response = await fetch('/api/upload_resume', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                showAlert('success', result.message);
                
                // Reset form
                resetForm();
                
                // Redirect to dashboard after 2 seconds
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 2000);
                
            } else {
                showAlert('error', result.message);
            }
            
        } catch (error) {
            console.error('Upload error:', error);
            showAlert('error', 'Upload failed. Please check your connection and try again.');
        } finally {
            setUploading(false);
        }
    }
    
    function setUploading(uploading) {
        if (uploading) {
            uploadBtn.disabled = true;
            uploadBtn.classList.add('loading');
            uploadBtn.textContent = 'Uploading...';
            progressContainer.style.display = 'block';
            
            // Simulate progress (in real app, you'd track actual progress)
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;
                
                progressFill.style.width = progress + '%';
                progressText.textContent = `Uploading... ${Math.round(progress)}%`;
                
                if (progress >= 90) {
                    clearInterval(interval);
                    progressText.textContent = 'Processing file...';
                }
            }, 200);
            
        } else {
            uploadBtn.disabled = false;
            uploadBtn.classList.remove('loading');
            uploadBtn.textContent = '📤 Upload Resume';
            progressContainer.style.display = 'none';
            progressFill.style.width = '0%';
        }
    }
    
    function resetForm() {
        selectedFile = null;
        fileInput.value = '';
        document.getElementById('resumeTitle').value = '';
        fileInfo.style.display = 'none';
        uploadBtn.disabled = true;
        uploadBtn.textContent = '📤 Upload Resume';
    }
    
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// UI Helper functions
function showAlert(type, message) {
    const alertContainer = document.getElementById('alertContainer');
    
    const alertHTML = `
        <div class="alert alert-${type}">
            <strong>${message}</strong>
        </div>
    `;
    
    alertContainer.innerHTML = alertHTML;
    
    // Scroll to top to show alert
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function clearAlerts() {
    document.getElementById('alertContainer').innerHTML = '';
}
