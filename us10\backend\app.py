from flask import Flask, render_template, send_from_directory
from flask_cors import CORS
from flask_jwt_extended import J<PERSON>TManager
import os
import sys

# Add error handling for imports
try:
    from models import db
    from config import Config
    print("✅ Core imports successful")
except Exception as e:
    print(f"❌ Core import error: {e}")
    sys.exit(1)

def create_app():
    """Application factory pattern"""
    print("🏗️ Creating Flask app...")

    app = Flask(__name__,
                template_folder='../frontend',
                static_folder='../frontend/static')

    # Load configuration
    print("⚙️ Loading configuration...")
    app.config.from_object(Config)

    # Initialize upload directories
    print("📁 Initializing directories...")
    Config.init_app(app)

    # Initialize extensions
    print("🔧 Initializing database...")
    db.init_app(app)

    print("🌐 Setting up CORS...")
    CORS(app, origins=app.config['CORS_ORIGINS'])

    # Initialize JWT
    print("🔐 Setting up JWT...")
    jwt = JWTManager(app)
    
    # JWT error handlers
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return {
            'success': False,
            'message': 'Token has expired',
            'error': 'token_expired'
        }, 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return {
            'success': False,
            'message': 'Invalid token',
            'error': 'invalid_token'
        }, 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return {
            'success': False,
            'message': 'Authorization token is required',
            'error': 'authorization_required'
        }, 401

    # Import and register blueprints with error handling
    print("📋 Importing routes...")
    try:
        from routes.us05_auth_routes import auth_bp
        from routes.us05_jd_routes import jd_bp
        from routes.us05_upload_routes import upload_bp
        from routes.us05_keyword_routes import keyword_bp
        from routes.us06_matching_routes import matching_bp
        from routes.us07_suggestions_routes import suggestions_bp
        from routes.us10_history_routes import history_bp
        from routes.us10_account_routes import account_bp
        print("✅ Routes imported successfully")
    except Exception as e:
        print(f"❌ Route import error: {e}")
        # Continue with basic app even if some routes fail

    print("🔗 Registering blueprints...")
    try:
        app.register_blueprint(auth_bp)
        app.register_blueprint(jd_bp)
        app.register_blueprint(upload_bp)
        app.register_blueprint(keyword_bp)
        app.register_blueprint(matching_bp)
        app.register_blueprint(suggestions_bp)
        app.register_blueprint(history_bp)
        app.register_blueprint(account_bp)
        print("✅ Blueprints registered successfully")
    except Exception as e:
        print(f"❌ Blueprint registration error: {e}")
        print("⚠️ Some features may not be available")
    
    # Frontend routes
    @app.route('/')
    def landing():
        """Serve the landing page"""
        return render_template('us10_landing.html')

    @app.route('/register')
    def register_page():
        """Serve the registration page"""
        return render_template('us10_register.html')

    @app.route('/login')
    def login_page():
        """Serve the login page"""
        return render_template('us10_login.html')

    @app.route('/dashboard')
    def dashboard_page():
        """Serve the dashboard page (protected)"""
        return render_template('us10_dashboard.html')

    @app.route('/add-job-description')
    def add_jd_page():
        """Serve the add job description page (protected)"""
        return render_template('us10_add_jd.html')

    @app.route('/upload')
    def upload_page():
        """Serve the upload page (protected)"""
        return render_template('us10_upload.html')

    @app.route('/keywords')
    def keywords_page():
        """Serve the keywords analysis page (protected)"""
        return render_template('us10_keywords.html')

    @app.route('/matching')
    def matching_page():
        """Serve the matching score page (protected)"""
        return render_template('us10_matching.html')

    @app.route('/suggestions')
    def suggestions_page():
        """Serve the suggestions page (protected)"""
        return render_template('us10_suggestions.html')

    @app.route('/account')
    def account_page():
        """Serve the account settings page (protected)"""
        return render_template('us10_account.html')

    # Health check endpoint
    @app.route('/health')
    def health_check():
        return {'status': 'healthy', 'service': 'Dr. Resume US-10'}, 200

    # Favicon route
    @app.route('/favicon.ico')
    def favicon():
        return '', 204  # No content for favicon
    
    # Create database tables
    with app.app_context():
        db.create_all()
        print("Database tables created successfully!")
    
    return app

if __name__ == '__main__':
    try:
        app = create_app()
        print("🩺" + "="*60 + "🩺")
        print("🚀 Dr. Resume US-10 Server Starting...")
        print("📋 All US-01 to US-10 functionalities included")
        print("="*64)
        print()
        print("🌐 FRONTEND PAGES:")
        print("📱 Landing Page:        http://localhost:5000")
        print("📝 Register:            http://localhost:5000/register")
        print("🔐 Login:               http://localhost:5000/login")
        print("📊 Dashboard:           http://localhost:5000/dashboard")
        print("📄 Job Description:     http://localhost:5000/add-job-description")
        print("📤 Upload Resume:       http://localhost:5000/upload")
        print("🔍 Keywords Analysis:   http://localhost:5000/keywords")
        print("🎯 Matching Score:      http://localhost:5000/matching")
        print("💡 Suggestions:         http://localhost:5000/suggestions")
        print("⚙️ Account Settings:    http://localhost:5000/account")
        print()
        print("🔌 API ENDPOINTS:")
        print("📝 Registration:        POST /api/register")
        print("🔐 Login:               POST /api/login")
        print("📤 Upload Resume:       POST /api/upload_resume")
        print("📄 Upload JD:           POST /api/upload_jd")
        print("🔍 Extract Keywords:    POST /api/extract_keywords")
        print("🎯 Calculate Match:     POST /api/calculate_match")
        print("💡 Get Suggestions:     GET /api/suggestions")
        print("🌟 Premium Suggestions: POST /api/premium_suggestions")
        print("📈 Scan History:        GET /api/history")
        print("📊 Dashboard Stats:     GET /api/dashboard_stats")
        print("⚙️ Account Info:        GET /api/account_info")
        print("🔧 Update Account:      PUT /api/update_account")
        print("💚 Health Check:        GET /health")
        print()
        print("="*64)
        print("🎉 Ready to serve requests!")
        print("🩺" + "="*60 + "🩺")

        app.run(debug=True, host='0.0.0.0', port=5000)

    except Exception as e:
        print(f"❌ Error starting application: {e}")
        import traceback
        traceback.print_exc()
