#!/usr/bin/env python3
"""
Simple database creation script
"""

import os
import sqlite3

# Get absolute path to database
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
db_path = os.path.join(project_root, 'shared', 'database', 'dr_resume_dev.db')

print(f"Creating database at: {db_path}")

# Ensure directory exists
os.makedirs(os.path.dirname(db_path), exist_ok=True)

# Create database and basic tables
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Create users table (US-01)
cursor.execute('''
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
''')

# Create resumes table (US-03)
cursor.execute('''
CREATE TABLE IF NOT EXISTS resumes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER NOT NULL,
    file_type VARCHAR(10) NOT NULL,
    extracted_text TEXT,
    title VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    upload_status VARCHAR(20) DEFAULT 'completed',
    error_message TEXT,
    keywords_extracted BOOLEAN DEFAULT FALSE,
    technical_skills TEXT,
    soft_skills TEXT,
    other_keywords TEXT,
    keyword_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
)
''')

# Create job_descriptions table (US-04)
cursor.execute('''
CREATE TABLE IF NOT EXISTS job_descriptions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    title VARCHAR(255) NOT NULL,
    company_name VARCHAR(255),
    job_text TEXT NOT NULL,
    keywords_extracted BOOLEAN DEFAULT FALSE,
    technical_skills TEXT,
    soft_skills TEXT,
    other_keywords TEXT,
    keyword_count INTEGER DEFAULT 0,
    word_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
)
''')

# Create match_scores table (US-06)
cursor.execute('''
CREATE TABLE IF NOT EXISTS match_scores (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    resume_id INTEGER NOT NULL,
    job_description_id INTEGER NOT NULL,
    overall_score REAL,
    technical_score REAL,
    soft_skills_score REAL,
    other_score REAL,
    matched_keywords TEXT,
    missing_keywords TEXT,
    algorithm_version VARCHAR(20) DEFAULT 'v1.0',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (resume_id) REFERENCES resumes (id),
    FOREIGN KEY (job_description_id) REFERENCES job_descriptions (id)
)
''')

# Create suggestions table (US-07)
cursor.execute('''
CREATE TABLE IF NOT EXISTS suggestions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    resume_id INTEGER NOT NULL,
    job_description_id INTEGER NOT NULL,
    suggestion_type VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    implemented BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (resume_id) REFERENCES resumes (id),
    FOREIGN KEY (job_description_id) REFERENCES job_descriptions (id)
)
''')

# Create test user
cursor.execute('''
INSERT OR IGNORE INTO users (first_name, last_name, email, password_hash)
VALUES ('Test', 'User', '<EMAIL>', 'pbkdf2:sha256:260000$test$test')
''')

conn.commit()
conn.close()

print("✅ Database created successfully!")
print(f"📍 Location: {db_path}")
print("👤 Test user: <EMAIL> / password123")
