#!/usr/bin/env python3
"""
Debug version of app.py to identify startup issues
"""

import sys
import traceback

def debug_startup():
    """Debug Flask app startup"""
    try:
        print("🔍 Starting Flask app debug...")
        
        # Test imports
        print("📦 Testing imports...")
        from flask import Flask
        print("✅ Flask imported")
        
        from models import db
        print("✅ Models imported")
        
        from config import Config
        print("✅ Config imported")
        
        # Test app creation
        print("🏗️ Creating Flask app...")
        app = Flask(__name__, 
                    template_folder='../frontend',
                    static_folder='../frontend/static')
        print("✅ Flask app created")
        
        # Test config loading
        print("⚙️ Loading configuration...")
        app.config.from_object(Config)
        print("✅ Configuration loaded")
        
        # Test database initialization
        print("🗄️ Initializing database...")
        db.init_app(app)
        print("✅ Database initialized")
        
        # Test app context
        print("🔧 Testing app context...")
        with app.app_context():
            print("✅ App context works")
        
        # Test routes import
        print("🛣️ Testing routes import...")
        from routes.us05_auth_routes import auth_bp
        from routes.us07_suggestions_routes import suggestions_bp
        print("✅ Routes imported")
        
        print("🎉 All tests passed! Starting Flask app...")
        
        # Start the app
        app.run(host='0.0.0.0', port=5000, debug=True)
        
    except Exception as e:
        print(f"❌ Error during startup: {e}")
        print("📋 Full traceback:")
        traceback.print_exc()
        return False
    
    return True

if __name__ == '__main__':
    debug_startup()
