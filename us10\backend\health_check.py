#!/usr/bin/env python3
"""
Comprehensive Health Check for Dr. Resume Application
Tests all components and dependencies
"""

import os
import sys
import json
from datetime import datetime

def test_imports():
    """Test all critical imports"""
    print("📦 Testing Imports...")
    
    try:
        # Core Flask
        from flask import Flask
        print("  ✅ Flask")
        
        # Database
        from models import db, User, Resume, JobDescription, Suggestion
        print("  ✅ Database Models")
        
        # Services
        from services.local_ai_suggestions_service import LocalAISuggestionsService
        print("  ✅ Local AI Service")
        
        from services.premium_suggestions_service import PremiumSuggestionsService
        print("  ✅ Premium Suggestions Service")
        
        from services.suggestions_service import SuggestionsService
        print("  ✅ Basic Suggestions Service")
        
        # NLP Libraries
        import nltk
        import spacy
        print("  ✅ NLP Libraries")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import Error: {e}")
        return False

def test_database():
    """Test database connectivity"""
    print("🗄️ Testing Database...")
    
    try:
        from models import db
        from config import Config
        from flask import Flask
        
        app = Flask(__name__)
        app.config.from_object(Config)
        db.init_app(app)
        
        with app.app_context():
            # Test database connection
            result = db.engine.execute("SELECT 1").fetchone()
            print("  ✅ Database Connection")
            
            # Test table existence
            tables = db.engine.table_names()
            required_tables = ['users', 'resumes', 'job_descriptions', 'suggestions']
            
            for table in required_tables:
                if table in tables:
                    print(f"  ✅ Table: {table}")
                else:
                    print(f"  ❌ Missing Table: {table}")
            
        return True
        
    except Exception as e:
        print(f"  ❌ Database Error: {e}")
        return False

def test_local_ai():
    """Test Local AI functionality"""
    print("🤖 Testing Local AI...")
    
    try:
        from services.local_ai_suggestions_service import LocalAISuggestionsService
        
        ai_service = LocalAISuggestionsService()
        
        # Test data
        resume_keywords = {
            'technical_skills': ['python', 'javascript', 'html'],
            'soft_skills': ['communication', 'teamwork'],
            'other_keywords': ['project management']
        }
        
        jd_keywords = {
            'technical_skills': ['python', 'react', 'sql'],
            'soft_skills': ['communication', 'leadership'],
            'other_keywords': ['agile', 'scrum']
        }
        
        # Generate suggestions
        result = ai_service.generate_local_ai_suggestions(
            resume_id=1,
            job_description_id=1,
            resume_keywords=resume_keywords,
            jd_keywords=jd_keywords,
            resume_text="Sample resume",
            jd_text="Sample job description"
        )
        
        # Verify result structure
        required_keys = ['analysis_summary', 'keyword_recommendations', 'ai_powered']
        for key in required_keys:
            if key in result:
                print(f"  ✅ {key}")
            else:
                print(f"  ❌ Missing: {key}")
        
        print(f"  ✅ Generated {len(result.get('keyword_recommendations', []))} recommendations")
        return True
        
    except Exception as e:
        print(f"  ❌ Local AI Error: {e}")
        return False

def test_nlp_models():
    """Test NLP model availability"""
    print("🧠 Testing NLP Models...")
    
    try:
        # Test NLTK
        import nltk
        
        try:
            nltk.data.find('tokenizers/punkt')
            print("  ✅ NLTK punkt")
        except LookupError:
            print("  ❌ NLTK punkt missing")
        
        try:
            nltk.data.find('corpora/stopwords')
            print("  ✅ NLTK stopwords")
        except LookupError:
            print("  ❌ NLTK stopwords missing")
        
        # Test SpaCy (optional)
        try:
            import spacy
            nlp = spacy.load('en_core_web_sm')
            print("  ✅ SpaCy en_core_web_sm")
        except OSError:
            print("  ⚠️ SpaCy model missing (optional)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ NLP Error: {e}")
        return False

def test_file_system():
    """Test file system setup"""
    print("📁 Testing File System...")
    
    try:
        # Check required directories
        required_dirs = [
            '../../shared/database',
            '../../shared/uploads',
            '../frontend/static',
            '../frontend'
        ]
        
        for dir_path in required_dirs:
            if os.path.exists(dir_path):
                print(f"  ✅ Directory: {dir_path}")
            else:
                print(f"  ❌ Missing: {dir_path}")
        
        # Check database file
        db_path = '../../shared/database/dr_resume_dev.db'
        if os.path.exists(db_path):
            size = os.path.getsize(db_path)
            print(f"  ✅ Database file: {size} bytes")
        else:
            print("  ❌ Database file missing")
        
        return True
        
    except Exception as e:
        print(f"  ❌ File System Error: {e}")
        return False

def main():
    """Run comprehensive health check"""
    print("🩺" + "="*50 + "🩺")
    print("🤖 Dr. Resume - Comprehensive Health Check")
    print("="*52)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Imports", test_imports),
        ("Database", test_database),
        ("Local AI", test_local_ai),
        ("NLP Models", test_nlp_models),
        ("File System", test_file_system)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"🔍 {test_name}:")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"  ❌ Test failed: {e}")
            results[test_name] = False
        print()
    
    # Summary
    print("="*52)
    print("📊 HEALTH CHECK SUMMARY")
    print("="*52)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:15} {status}")
    
    print()
    print(f"Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL SYSTEMS HEALTHY!")
        print("🚀 Dr. Resume is ready to use!")
    else:
        print("⚠️ Some issues detected")
        print("🔧 Please fix the failing components")
    
    print("🩺" + "="*50 + "🩺")

if __name__ == '__main__':
    main()
