#!/usr/bin/env python3
"""
Complete Database Migration for US-10
Creates all tables from US-01 through US-10 sequential evolution
"""

import os
import sys
from flask import Flask
from models import db, User, Resume, JobDescription, MatchScore, Suggestion
from config import Config

def create_complete_database():
    """Create complete database schema for US-10"""
    print("🗄️ Creating Complete Database Schema...")
    
    # Create Flask app
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Initialize database
    db.init_app(app)
    
    with app.app_context():
        try:
            # Drop all tables (fresh start)
            print("🧹 Dropping existing tables...")
            db.drop_all()
            
            # Create all tables
            print("🏗️ Creating all tables...")
            db.create_all()
            
            # Verify tables were created
            print("✅ Verifying table creation...")
            tables = db.engine.table_names()
            
            expected_tables = [
                'users',           # US-01: User registration
                'resumes',         # US-03: Resume upload
                'job_descriptions', # US-04: Job descriptions
                'match_scores',    # US-06: Matching results
                'suggestions'      # US-07: AI suggestions
            ]
            
            for table in expected_tables:
                if table in tables:
                    print(f"  ✅ {table}")
                else:
                    print(f"  ❌ Missing: {table}")
            
            # Create test user for development
            print("👤 Creating test user...")
            test_user = User(
                first_name="Test",
                last_name="User", 
                email="<EMAIL>"
            )
            test_user.set_password("password123")
            
            db.session.add(test_user)
            db.session.commit()
            
            print(f"✅ Test user created with ID: {test_user.id}")
            
            print("🎉 Database migration completed successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Database migration failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def verify_database():
    """Verify database integrity"""
    print("\n🔍 Verifying Database Integrity...")
    
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    
    with app.app_context():
        try:
            # Test basic queries
            user_count = User.query.count()
            print(f"  👥 Users: {user_count}")
            
            resume_count = Resume.query.count()
            print(f"  📄 Resumes: {resume_count}")
            
            jd_count = JobDescription.query.count()
            print(f"  💼 Job Descriptions: {jd_count}")
            
            suggestion_count = Suggestion.query.count()
            print(f"  💡 Suggestions: {suggestion_count}")
            
            print("✅ Database verification completed!")
            return True
            
        except Exception as e:
            print(f"❌ Database verification failed: {e}")
            return False

def main():
    """Main migration function"""
    print("🩺" + "="*50 + "🩺")
    print("🗄️ Dr. Resume Database Migration")
    print("="*52)
    print("📋 Creating complete US-01→US-10 schema")
    print()
    
    # Ensure directories exist
    config = Config()
    os.makedirs(os.path.dirname(config.SQLALCHEMY_DATABASE_URI.replace('sqlite:///', '')), exist_ok=True)
    os.makedirs(config.UPLOAD_FOLDER, exist_ok=True)
    os.makedirs(config.RESUME_UPLOAD_FOLDER, exist_ok=True)
    
    # Run migration
    if create_complete_database():
        if verify_database():
            print("\n🎉 MIGRATION SUCCESSFUL!")
            print("🚀 Database ready for US-10 application")
        else:
            print("\n⚠️ Migration completed but verification failed")
    else:
        print("\n❌ MIGRATION FAILED!")
        print("🔧 Please check the errors above")
    
    print("🩺" + "="*50 + "🩺")

if __name__ == '__main__':
    main()
