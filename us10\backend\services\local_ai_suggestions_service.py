#!/usr/bin/env python3
"""
Local AI-like Suggestions Service
Generates intelligent, natural-sounding suggestions using keyword analysis and rule-based templates
No external API required - fully local processing
"""

import json
import random
from typing import Dict, List, Set, Tuple
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LocalAISuggestionsService:
    """
    Local AI-like suggestion generator using keyword analysis and intelligent templates
    """
    
    def __init__(self):
        self.logger = logger
        
        # Skill relationship mapping for intelligent suggestions
        self.skill_relationships = {
            'python': ['django', 'flask', 'fastapi', 'pandas', 'numpy', 'scikit-learn'],
            'javascript': ['react', 'vue', 'angular', 'node.js', 'express', 'typescript'],
            'java': ['spring', 'hibernate', 'maven', 'gradle', 'junit'],
            'react': ['redux', 'next.js', 'typescript', 'javascript', 'html', 'css'],
            'aws': ['ec2', 's3', 'lambda', 'rds', 'cloudformation', 'docker'],
            'docker': ['kubernetes', 'aws', 'devops', 'ci/cd', 'jenkins'],
            'sql': ['mysql', 'postgresql', 'mongodb', 'database', 'data analysis'],
            'machine learning': ['python', 'tensorflow', 'pytorch', 'scikit-learn', 'pandas'],
            'devops': ['docker', 'kubernetes', 'jenkins', 'aws', 'ci/cd', 'terraform']
        }
        
        # Priority skill categories
        self.high_priority_skills = {
            'python', 'javascript', 'react', 'aws', 'docker', 'kubernetes', 
            'sql', 'git', 'agile', 'scrum', 'machine learning', 'data analysis'
        }
        
        # Suggestion templates for natural language generation
        self.suggestion_templates = {
            'matched_skills': [
                "✅ Excellent! You already have {skill} experience. Make sure to highlight this prominently in your resume.",
                "✅ Great match! Your {skill} skills align perfectly with the job requirements.",
                "✅ Strong foundation! Your {skill} experience is exactly what they're looking for."
            ],
            'missing_critical': [
                "🚨 Critical Gap: {skill} is essential for this role. Consider adding projects or certifications to demonstrate this skill.",
                "🚨 High Priority: {skill} appears to be a core requirement. This should be your top learning priority.",
                "🚨 Must-Have: {skill} is mentioned multiple times in the job description. Focus on gaining this skill immediately."
            ],
            'missing_important': [
                "⚠️ Important: {skill} would strengthen your application significantly.",
                "⚠️ Recommended: Adding {skill} to your skillset would make you more competitive.",
                "⚠️ Consider Learning: {skill} is valued for this position and worth developing."
            ],
            'related_suggestions': [
                "💡 Since you know {base_skill}, learning {related_skill} would be a natural next step.",
                "💡 Your {base_skill} background makes you well-positioned to learn {related_skill}.",
                "💡 Complement your {base_skill} skills by adding {related_skill} to your toolkit."
            ],
            'extra_skills': [
                "🌟 Bonus: Your {skill} experience could give you an edge over other candidates.",
                "🌟 Additional Value: Your {skill} skills add extra value beyond the basic requirements.",
                "🌟 Differentiator: Your {skill} background sets you apart from typical candidates."
            ]
        }
    
    def generate_local_ai_suggestions(self, resume_id: int, job_description_id: int, 
                                    resume_keywords: Dict, jd_keywords: Dict, 
                                    resume_text: str = "", jd_text: str = "") -> Dict:
        """
        Generate AI-like suggestions using local keyword analysis and intelligent templates
        
        Args:
            resume_id: Resume ID
            job_description_id: Job description ID  
            resume_keywords: Resume keywords by category
            jd_keywords: Job description keywords by category
            resume_text: Full resume text (optional)
            jd_text: Full job description text (optional)
            
        Returns:
            Dict containing AI-like suggestions
        """
        try:
            # Step 1: Classify keywords
            classification = self._classify_keywords(resume_keywords, jd_keywords)
            
            # Step 2: Generate intelligent suggestions
            suggestions = self._generate_intelligent_suggestions(classification)
            
            # Step 3: Add contextual recommendations
            contextual_recs = self._generate_contextual_recommendations(
                classification, resume_text, jd_text
            )
            
            # Step 4: Create structured output
            result = {
                'type': 'local_ai_powered',
                'analysis_summary': self._create_analysis_summary(classification),
                'keyword_recommendations': suggestions['keyword_recommendations'],
                'structure_improvements': suggestions['structure_improvements'],
                'ats_optimizations': suggestions['ats_optimizations'],
                'quick_wins': suggestions['quick_wins'],
                'contextual_advice': contextual_recs,
                'skill_development_path': self._create_skill_development_path(classification),
                'match_analysis': classification,
                'generated_at': datetime.utcnow().isoformat(),
                'ai_powered': True,
                'local_processing': True
            }
            
            self.logger.info(f"Generated local AI suggestions for resume {resume_id} vs JD {job_description_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error generating local AI suggestions: {e}")
            return self._generate_fallback_suggestions()
    
    def _classify_keywords(self, resume_keywords: Dict, jd_keywords: Dict) -> Dict:
        """
        Classify keywords into matched, missing, extra, and related categories
        """
        classification = {
            'matched': {'technical_skills': [], 'soft_skills': [], 'other_keywords': []},
            'missing': {'technical_skills': [], 'soft_skills': [], 'other_keywords': []},
            'extra': {'technical_skills': [], 'soft_skills': [], 'other_keywords': []},
            'related_opportunities': [],
            'priority_missing': [],
            'critical_gaps': []
        }
        
        for category in ['technical_skills', 'soft_skills', 'other_keywords']:
            resume_set = set(kw.lower().strip() for kw in resume_keywords.get(category, []))
            jd_set = set(kw.lower().strip() for kw in jd_keywords.get(category, []))
            
            # Find matches, missing, and extra
            matched = list(resume_set & jd_set)
            missing = list(jd_set - resume_set)
            extra = list(resume_set - jd_set)
            
            classification['matched'][category] = matched
            classification['missing'][category] = missing
            classification['extra'][category] = extra
            
            # Identify priority and critical gaps
            if category == 'technical_skills':
                for skill in missing:
                    if skill in self.high_priority_skills:
                        classification['priority_missing'].append(skill)
                    if self._is_critical_skill(skill, jd_keywords):
                        classification['critical_gaps'].append(skill)
        
        # Find related skill opportunities
        classification['related_opportunities'] = self._find_related_opportunities(
            classification['matched'], classification['missing']
        )
        
        return classification
    
    def _generate_intelligent_suggestions(self, classification: Dict) -> Dict:
        """
        Generate intelligent, natural-sounding suggestions based on classification
        """
        suggestions = {
            'keyword_recommendations': [],
            'structure_improvements': [],
            'ats_optimizations': [],
            'quick_wins': []
        }
        
        # Keyword recommendations based on analysis
        suggestions['keyword_recommendations'] = self._generate_keyword_recommendations(classification)
        
        # Structure improvements
        suggestions['structure_improvements'] = [
            "Create a dedicated 'Technical Skills' section to highlight your technical competencies",
            "Add a 'Professional Summary' that incorporates key job requirements",
            "Use bullet points to improve readability and ATS scanning",
            "Organize experience in reverse chronological order with clear job titles"
        ]
        
        # ATS optimizations
        suggestions['ats_optimizations'] = [
            "Use standard section headers like 'Experience', 'Education', 'Skills'",
            "Include exact keyword matches from the job description",
            "Save resume in both PDF and Word formats for different ATS systems",
            "Avoid graphics, tables, and complex formatting that ATS can't read"
        ]
        
        # Quick wins
        suggestions['quick_wins'] = self._generate_quick_wins(classification)
        
        return suggestions

    def _generate_keyword_recommendations(self, classification: Dict) -> List[str]:
        """Generate natural keyword recommendations"""
        recommendations = []

        # Celebrate matched skills
        for category in classification['matched']:
            for skill in classification['matched'][category][:3]:  # Top 3 matches
                template = random.choice(self.suggestion_templates['matched_skills'])
                recommendations.append(template.format(skill=skill.title()))

        # Address critical gaps
        for skill in classification['critical_gaps'][:2]:  # Top 2 critical
            template = random.choice(self.suggestion_templates['missing_critical'])
            recommendations.append(template.format(skill=skill.title()))

        # Address important missing skills
        for skill in classification['priority_missing'][:3]:  # Top 3 priority
            if skill not in classification['critical_gaps']:
                template = random.choice(self.suggestion_templates['missing_important'])
                recommendations.append(template.format(skill=skill.title()))

        # Suggest related skills
        for opportunity in classification['related_opportunities'][:2]:
            template = random.choice(self.suggestion_templates['related_suggestions'])
            recommendations.append(template.format(
                base_skill=opportunity['base_skill'].title(),
                related_skill=opportunity['related_skill'].title()
            ))

        # Highlight extra value
        extra_skills = []
        for category in classification['extra']:
            extra_skills.extend(classification['extra'][category])

        for skill in extra_skills[:2]:  # Top 2 extra skills
            template = random.choice(self.suggestion_templates['extra_skills'])
            recommendations.append(template.format(skill=skill.title()))

        return recommendations[:8]  # Limit to 8 recommendations

    def _generate_quick_wins(self, classification: Dict) -> List[str]:
        """Generate actionable quick wins"""
        quick_wins = []

        # Based on matched skills
        matched_count = sum(len(classification['matched'][cat]) for cat in classification['matched'])
        if matched_count > 0:
            quick_wins.append(f"Emphasize your {matched_count} matching skills in your summary section")

        # Based on missing skills
        missing_count = sum(len(classification['missing'][cat]) for cat in classification['missing'])
        if missing_count > 0:
            quick_wins.append(f"Address the {missing_count} missing keywords through projects or learning")

        # Specific actionable items
        if classification['critical_gaps']:
            quick_wins.append("Focus on the most critical missing skills first for maximum impact")

        if classification['priority_missing']:
            quick_wins.append("Add online courses or certifications for high-priority missing skills")

        quick_wins.extend([
            "Quantify your achievements with specific numbers and percentages",
            "Tailor your professional summary to mirror the job description language",
            "Include relevant keywords naturally throughout your experience descriptions"
        ])

        return quick_wins[:6]  # Limit to 6 quick wins

    def _create_analysis_summary(self, classification: Dict) -> str:
        """Create an intelligent analysis summary"""
        matched_total = sum(len(classification['matched'][cat]) for cat in classification['matched'])
        missing_total = sum(len(classification['missing'][cat]) for cat in classification['missing'])

        if matched_total == 0:
            match_level = "significant gaps"
        elif matched_total < 3:
            match_level = "some alignment"
        elif matched_total < 6:
            match_level = "good alignment"
        else:
            match_level = "strong alignment"

        summary = f"Your resume shows {match_level} with the job requirements. "
        summary += f"You have {matched_total} matching skills and {missing_total} areas for improvement. "

        if classification['critical_gaps']:
            summary += f"Focus on {len(classification['critical_gaps'])} critical skill gaps for best results."
        else:
            summary += "No critical gaps identified - focus on optimization and presentation."

        return summary

    def _create_skill_development_path(self, classification: Dict) -> List[Dict]:
        """Create a learning path for skill development"""
        path = []

        # Priority 1: Critical gaps
        for skill in classification['critical_gaps'][:3]:
            path.append({
                'skill': skill.title(),
                'priority': 'Critical',
                'timeframe': '1-2 weeks',
                'action': f'Take online course or complete project demonstrating {skill}',
                'resources': ['Coursera', 'Udemy', 'YouTube tutorials', 'Official documentation']
            })

        # Priority 2: High-value missing skills
        for skill in classification['priority_missing'][:2]:
            if skill not in classification['critical_gaps']:
                path.append({
                    'skill': skill.title(),
                    'priority': 'High',
                    'timeframe': '2-4 weeks',
                    'action': f'Build portfolio project using {skill}',
                    'resources': ['GitHub projects', 'FreeCodeCamp', 'Codecademy']
                })

        # Priority 3: Related skills for expansion
        for opportunity in classification['related_opportunities'][:2]:
            path.append({
                'skill': opportunity['related_skill'].title(),
                'priority': 'Medium',
                'timeframe': '1-2 months',
                'action': f'Expand from {opportunity["base_skill"]} to {opportunity["related_skill"]}',
                'resources': ['Advanced tutorials', 'Certification programs', 'Practice projects']
            })

        return path

    def _generate_contextual_recommendations(self, classification: Dict,
                                           resume_text: str, jd_text: str) -> List[str]:
        """Generate contextual recommendations based on text analysis"""
        recommendations = []

        # Analyze text patterns (simplified)
        if resume_text and jd_text:
            jd_lower = jd_text.lower()

            # Check for specific patterns
            if 'experience' in jd_lower and 'years' in jd_lower:
                recommendations.append("Quantify your experience with specific years and project counts")

            if 'team' in jd_lower or 'collaboration' in jd_lower:
                recommendations.append("Highlight team collaboration and leadership experiences")

            if 'project' in jd_lower:
                recommendations.append("Emphasize project management and delivery capabilities")

        # Add general contextual advice
        recommendations.extend([
            "Use action verbs to start each bullet point (Developed, Implemented, Led, etc.)",
            "Include metrics and results wherever possible (increased efficiency by 25%)",
            "Match the tone and language style used in the job description"
        ])

        return recommendations[:5]

    def _find_related_opportunities(self, matched: Dict, missing: Dict) -> List[Dict]:
        """Find opportunities to learn related skills based on existing knowledge"""
        opportunities = []

        # Get all matched skills
        all_matched = []
        for category in matched:
            all_matched.extend(matched[category])

        # Get all missing skills
        all_missing = []
        for category in missing:
            all_missing.extend(missing[category])

        # Find relationships
        for matched_skill in all_matched:
            if matched_skill.lower() in self.skill_relationships:
                related_skills = self.skill_relationships[matched_skill.lower()]
                for related in related_skills:
                    if related in [m.lower() for m in all_missing]:
                        opportunities.append({
                            'base_skill': matched_skill,
                            'related_skill': related,
                            'relationship': 'complementary'
                        })

        return opportunities[:3]  # Limit to 3 opportunities

    def _is_critical_skill(self, skill: str, jd_keywords: Dict) -> bool:
        """Determine if a skill is critical based on job description analysis"""
        # Simple heuristic: if it appears in technical skills and is high priority
        return (skill.lower() in self.high_priority_skills and
                skill in jd_keywords.get('technical_skills', []))

    def _generate_fallback_suggestions(self) -> Dict:
        """Generate basic fallback suggestions if main process fails"""
        return {
            'type': 'fallback',
            'analysis_summary': 'Unable to complete full analysis. Please try again.',
            'keyword_recommendations': [
                'Review the job description for key technical skills',
                'Ensure your resume includes relevant industry keywords',
                'Highlight your most relevant experience prominently'
            ],
            'structure_improvements': [
                'Use clear section headers',
                'Organize content logically',
                'Keep formatting simple and clean'
            ],
            'ats_optimizations': [
                'Use standard fonts and formatting',
                'Include keywords from job description',
                'Save in multiple formats (PDF and Word)'
            ],
            'quick_wins': [
                'Proofread for spelling and grammar',
                'Quantify achievements with numbers',
                'Customize for each application'
            ],
            'generated_at': datetime.utcnow().isoformat(),
            'ai_powered': False,
            'local_processing': True
        }
