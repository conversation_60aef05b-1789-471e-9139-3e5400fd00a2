"""
US-07.1: Premium Suggestions Service
OpenAI-powered suggestions for enhanced resume optimization
"""

import logging
import os
import json
from typing import Dict, List, Optional
from models import db, Resume, JobDescription, MatchScore
from datetime import datetime

# Import local AI service
from .local_ai_suggestions_service import LocalAISuggestionsService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("OpenAI library not installed. Premium suggestions will use fallback logic.")


class PremiumSuggestionsService:
    """
    Service for generating AI-powered premium suggestions using OpenAI API
    """
    
    def __init__(self):
        self.logger = logger
        self.openai_available = OPENAI_AVAILABLE

        # Initialize Local AI service (always available)
        self.local_ai = LocalAISuggestionsService()

        # Initialize OpenAI if available
        if self.openai_available:
            api_key = os.getenv('OPENAI_API_KEY')
            if api_key and api_key != 'your-openai-api-key-here':
                self.client = OpenAI(api_key=api_key)
                self.logger.info("OpenAI API initialized successfully")
            else:
                self.openai_available = False
                self.client = None
                self.logger.info("Using Local AI - OpenAI API key not configured")
        else:
            self.client = None
            self.logger.info("Using Local AI - OpenAI library not available")
        
        # Fallback templates for when OpenAI is not available
        self.fallback_templates = {
            'comprehensive': self._get_comprehensive_template(),
            'quick': self._get_quick_template(),
            'targeted': self._get_targeted_template()
        }
    
    def generate_premium_suggestions(self, resume_id: int, job_description_id: int, 
                                   user_id: int, suggestion_type: str = 'comprehensive') -> Dict:
        """
        Generate premium AI-powered suggestions
        
        Args:
            resume_id: ID of the resume
            job_description_id: ID of the job description
            user_id: ID of the user
            suggestion_type: Type of suggestions ('comprehensive', 'quick', 'targeted')
            
        Returns:
            Dict containing AI-generated suggestions and recommendations
        """
        try:
            # Get resume and job description
            resume = Resume.query.filter_by(
                id=resume_id,
                user_id=user_id,
                is_active=True
            ).first()
            
            job_description = JobDescription.query.filter_by(
                id=job_description_id,
                user_id=user_id,
                is_active=True
            ).first()
            
            if not resume or not job_description:
                raise ValueError("Resume or job description not found")
            
            if not resume.keywords_extracted or not job_description.keywords_extracted:
                raise ValueError("Keywords not extracted for resume or job description")
            
            # Get existing match score
            match_score = MatchScore.query.filter_by(
                resume_id=resume_id,
                job_description_id=job_description_id,
                user_id=user_id,
                is_active=True
            ).first()
            
            # Prepare data for AI analysis
            analysis_data = self._prepare_analysis_data(resume, job_description, match_score)
            
            # Generate suggestions using OpenAI or Local AI
            if self.openai_available and self.client:
                suggestions = self._generate_openai_suggestions(analysis_data, suggestion_type)
            else:
                # Use Local AI instead of basic fallback
                suggestions = self._generate_local_ai_suggestions(resume, job_description, analysis_data, suggestion_type)
            
            self.logger.info(f"Generated premium suggestions for resume {resume_id} vs JD {job_description_id}")
            
            return {
                'success': True,
                'suggestions': suggestions,
                'analysis_data': analysis_data,
                'suggestion_type': suggestion_type,
                'ai_powered': self.openai_available,
                'generated_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error generating premium suggestions: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _prepare_analysis_data(self, resume: Resume, job_description: JobDescription, 
                             match_score: Optional[MatchScore]) -> Dict:
        """
        Prepare comprehensive data for AI analysis
        """
        resume_keywords = resume.get_keywords()
        jd_keywords = job_description.get_keywords()
        
        # Calculate missing keywords
        missing_keywords = {}
        for category in ['technical_skills', 'soft_skills', 'other_keywords']:
            resume_set = set(kw.lower().strip() for kw in resume_keywords[category])
            jd_set = set(kw.lower().strip() for kw in jd_keywords[category])
            missing_keywords[category] = list(jd_set - resume_set)
        
        return {
            'resume': {
                'filename': resume.original_filename,
                'title': resume.title,
                'text_excerpt': resume.extracted_text[:1000] if resume.extracted_text else '',
                'keywords': resume_keywords,
                'keyword_count': resume.keyword_count
            },
            'job_description': {
                'title': job_description.title,
                'company': job_description.company_name,
                'text_excerpt': job_description.job_text[:1000] if job_description.job_text else '',
                'keywords': jd_keywords,
                'keyword_count': job_description.keyword_count
            },
            'missing_keywords': missing_keywords,
            'match_score': {
                'overall': match_score.overall_score if match_score else None,
                'technical': match_score.technical_score if match_score else None,
                'soft_skills': match_score.soft_skills_score if match_score else None,
                'other': match_score.other_keywords_score if match_score else None
            },
            'analysis_summary': {
                'total_missing': sum(len(keywords) for keywords in missing_keywords.values()),
                'missing_technical': len(missing_keywords['technical_skills']),
                'missing_soft_skills': len(missing_keywords['soft_skills']),
                'missing_other': len(missing_keywords['other_keywords'])
            }
        }

    def _generate_local_ai_suggestions(self, resume, job_description, analysis_data: Dict, suggestion_type: str) -> Dict:
        """
        Generate suggestions using Local AI service
        """
        try:
            # Get keywords from resume and job description
            resume_keywords = resume.get_keywords()
            jd_keywords = job_description.get_keywords()

            # Get full text content
            resume_text = resume.extracted_text or ""
            jd_text = job_description.job_text or ""

            # Generate local AI suggestions
            local_suggestions = self.local_ai.generate_local_ai_suggestions(
                resume_id=resume.id,
                job_description_id=job_description.id,
                resume_keywords=resume_keywords,
                jd_keywords=jd_keywords,
                resume_text=resume_text,
                jd_text=jd_text
            )

            # Format for frontend compatibility
            return {
                'type': 'local_ai_powered',
                'content': {
                    'summary': local_suggestions['analysis_summary'],
                    'keyword_recommendations': local_suggestions['keyword_recommendations'],
                    'structure_improvements': local_suggestions['structure_improvements'],
                    'ats_optimizations': local_suggestions['ats_optimizations'],
                    'quick_wins': local_suggestions['quick_wins'],
                    'skill_development_path': local_suggestions.get('skill_development_path', []),
                    'contextual_advice': local_suggestions.get('contextual_advice', [])
                },
                'ai_powered': True,
                'local_processing': True,
                'model_used': 'Local AI Engine',
                'suggestion_type': suggestion_type
            }

        except Exception as e:
            self.logger.error(f"Local AI error: {e}")
            # Fallback to basic suggestions
            return self._generate_fallback_suggestions(analysis_data, suggestion_type)

    def _generate_openai_suggestions(self, analysis_data: Dict, suggestion_type: str) -> Dict:
        """
        Generate suggestions using OpenAI API
        """
        try:
            # Create prompt based on suggestion type
            prompt = self._create_openai_prompt(analysis_data, suggestion_type)
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert resume optimization consultant with deep knowledge of ATS systems and hiring practices."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1500,
                temperature=0.7
            )
            
            # Parse response
            ai_response = response.choices[0].message.content
            
            # Structure the response
            return {
                'type': 'ai_powered',
                'content': self._parse_ai_response(ai_response),
                'raw_response': ai_response,
                'model_used': 'gpt-3.5-turbo',
                'tokens_used': response.usage.total_tokens if response.usage else None
            }
            
        except Exception as e:
            self.logger.error(f"OpenAI API error: {e}")
            # Fallback to local suggestions
            return self._generate_fallback_suggestions(analysis_data, suggestion_type)
    
    def _create_openai_prompt(self, analysis_data: Dict, suggestion_type: str) -> str:
        """
        Create highly optimized, intelligent prompt for OpenAI based on analysis data
        """
        base_prompt = f"""
        You are an expert resume optimization consultant with 15+ years of experience helping professionals land their dream jobs.
        Analyze this resume against the job description and provide specific, actionable, and immediately implementable suggestions.

        CONTEXT:
        - Current match score: {analysis_data['match_score']['overall']}% (Target: 80%+)
        - Industry: {analysis_data['job_description'].get('industry', 'Technology')}
        - Seniority Level: {self._detect_seniority_level(analysis_data)}

        RESUME ANALYSIS:
        - Title: {analysis_data['resume']['title']}
        - Current Keywords: {analysis_data['resume']['keyword_count']} identified
        - Content Preview: {analysis_data['resume']['text_excerpt'][:500]}...

        TARGET JOB:
        - Position: {analysis_data['job_description']['title']}
        - Company: {analysis_data['job_description']['company']}
        - Required Keywords: {analysis_data['job_description']['keyword_count']} identified
        - Key Requirements: {analysis_data['job_description']['text_excerpt'][:500]}...

        CRITICAL GAPS IDENTIFIED:
        - Missing Technical Skills: {analysis_data['missing_keywords']['technical_skills']}
        - Missing Soft Skills: {analysis_data['missing_keywords']['soft_skills']}
        - Missing Industry Terms: {analysis_data['missing_keywords']['other_keywords']}

        PERFORMANCE INDICATORS:
        - ATS Compatibility: {self._assess_ats_compatibility(analysis_data)}
        - Keyword Density: {self._calculate_keyword_density(analysis_data)}
        - Quantified Achievements: {self._count_quantified_achievements(analysis_data)}
        """
        
        if suggestion_type == 'comprehensive':
            prompt_suffix = """
            COMPREHENSIVE OPTIMIZATION STRATEGY:

            Provide a complete resume transformation plan with:

            1. IMMEDIATE ACTIONS (Next 30 minutes):
               - Top 5 critical keywords to add with exact placement locations
               - 3 bullet points to rewrite for maximum impact
               - Quick ATS compatibility fixes

            2. CONTENT ENHANCEMENT (Next 2 hours):
               - Specific examples of quantified achievements to add
               - Industry-specific terminology integration
               - Skills section optimization with proficiency levels
               - Professional summary rewrite suggestions

            3. STRUCTURAL IMPROVEMENTS (Next day):
               - Section reordering for maximum impact
               - Formatting recommendations for ATS and human readers
               - Length optimization (target: 1-2 pages)
               - Visual hierarchy improvements

            4. STRATEGIC POSITIONING (Ongoing):
               - Personal brand alignment with target role
               - Career progression narrative strengthening
               - Industry trend integration
               - Competitive differentiation strategies

            Format each suggestion with:
            - Specific action item
            - Exact text/phrase recommendations
            - Expected impact on match score
            - Implementation difficulty (Easy/Medium/Hard)
            """
        elif suggestion_type == 'quick':
            prompt_suffix = """
            RAPID IMPACT OPTIMIZATION (30-minute implementation):

            Focus on highest-ROI changes that can be implemented immediately:

            1. CRITICAL KEYWORDS (Add these exact phrases):
               - Provide 5 specific keywords with exact placement instructions
               - Include context for natural integration
               - Specify section and position (e.g., "Add to 2nd bullet point in current role")

            2. POWER PHRASES (Replace weak language):
               - Identify 3 weak phrases currently in resume
               - Provide strong alternatives with action verbs
               - Include quantification opportunities

            3. ATS QUICK WINS:
               - Format fixes for better parsing
               - Section header optimizations
               - File naming and submission tips

            4. IMMEDIATE IMPACT METRICS:
               - Predicted match score improvement
               - Time to implement each change
               - Priority ranking (1-5 scale)

            Each suggestion must be implementable in under 5 minutes.
            """
        else:  # targeted
            prompt_suffix = """
            PRECISION TARGETING STRATEGY:

            Focus on closing the most critical gaps identified in the analysis:

            1. SKILL GAP CLOSURE:
               - Address each missing technical skill with specific integration strategy
               - Provide context-appropriate examples for each skill
               - Suggest relevant project descriptions or achievements

            2. INDUSTRY ALIGNMENT:
               - Integrate industry-specific terminology naturally
               - Align experience descriptions with target company culture
               - Position background to match role requirements exactly

            3. COMPETITIVE POSITIONING:
               - Identify unique value propositions to emphasize
               - Suggest differentiating achievements to highlight
               - Recommend positioning against typical candidates

            4. MATCH SCORE OPTIMIZATION:
               - Target specific improvements to reach 80%+ match
               - Prioritize changes by impact on algorithmic scoring
               - Balance keyword density with readability

            Provide surgical precision recommendations that directly address the {analysis_data['match_score']['overall']}% current score.
            """
        
        return base_prompt + prompt_suffix + "\n\nFormat your response as actionable bullet points with specific examples."
    
    def _parse_ai_response(self, ai_response: str) -> Dict:
        """
        Parse and structure AI response into organized suggestions
        """
        # Simple parsing - in production, you might want more sophisticated parsing
        lines = ai_response.split('\n')
        
        suggestions = {
            'summary': '',
            'keyword_recommendations': [],
            'structure_improvements': [],
            'ats_optimizations': [],
            'industry_specific': [],
            'quick_wins': []
        }
        
        current_section = 'summary'
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Detect section headers
            if 'keyword' in line.lower():
                current_section = 'keyword_recommendations'
            elif 'structure' in line.lower() or 'format' in line.lower():
                current_section = 'structure_improvements'
            elif 'ats' in line.lower():
                current_section = 'ats_optimizations'
            elif 'industry' in line.lower():
                current_section = 'industry_specific'
            elif 'quick' in line.lower() or 'immediate' in line.lower():
                current_section = 'quick_wins'
            
            # Add content to appropriate section
            if line.startswith('-') or line.startswith('•') or line.startswith('*'):
                suggestions[current_section].append(line[1:].strip())
            elif current_section == 'summary' and len(line) > 20:
                suggestions['summary'] += line + ' '
        
        return suggestions
    
    def _generate_fallback_suggestions(self, analysis_data: Dict, suggestion_type: str) -> Dict:
        """
        Generate suggestions using local logic when OpenAI is not available
        """
        template = self.fallback_templates.get(suggestion_type, self.fallback_templates['comprehensive'])
        
        # Customize template with actual data
        missing_tech = analysis_data['missing_keywords']['technical_skills'][:5]
        missing_soft = analysis_data['missing_keywords']['soft_skills'][:3]
        match_score = analysis_data['match_score']['overall'] or 0
        
        suggestions = {
            'type': 'fallback_logic',
            'content': {
                'summary': template['summary'].format(
                    match_score=match_score,
                    missing_count=analysis_data['analysis_summary']['total_missing']
                ),
                'keyword_recommendations': [
                    f"Add technical skill: {skill}" for skill in missing_tech
                ] + [
                    f"Highlight soft skill: {skill}" for skill in missing_soft
                ],
                'structure_improvements': template['structure_improvements'],
                'ats_optimizations': template['ats_optimizations'],
                'industry_specific': template['industry_specific'],
                'quick_wins': template['quick_wins']
            }
        }
        
        return suggestions
    
    def _get_comprehensive_template(self) -> Dict:
        return {
            'summary': 'Your resume has a {match_score}% match with {missing_count} missing keywords. Comprehensive optimization recommended.',
            'structure_improvements': [
                'Use bullet points for better ATS scanning',
                'Include a professional summary section',
                'Organize experience in reverse chronological order',
                'Use consistent formatting throughout'
            ],
            'ats_optimizations': [
                'Use standard section headers (Experience, Education, Skills)',
                'Avoid graphics, tables, and complex formatting',
                'Save as both PDF and Word formats',
                'Include keywords naturally in context'
            ],
            'industry_specific': [
                'Research industry-specific terminology',
                'Include relevant certifications and training',
                'Highlight measurable achievements',
                'Use action verbs appropriate for your field'
            ],
            'quick_wins': [
                'Add missing technical skills you possess',
                'Quantify your achievements with numbers',
                'Tailor your professional summary',
                'Include relevant keywords in job descriptions'
            ]
        }
    
    def _get_quick_template(self) -> Dict:
        return {
            'summary': 'Quick optimization for {match_score}% match score. Focus on high-impact changes.',
            'structure_improvements': [
                'Reorganize skills section to highlight relevant abilities',
                'Update professional summary with job-specific keywords'
            ],
            'ats_optimizations': [
                'Ensure keywords appear in multiple sections',
                'Use exact keyword phrases from job description'
            ],
            'industry_specific': [
                'Include industry buzzwords naturally',
                'Highlight relevant project experience'
            ],
            'quick_wins': [
                'Add top 5 missing technical skills',
                'Include 2-3 missing soft skills',
                'Update job titles to match industry standards'
            ]
        }
    
    def _get_targeted_template(self) -> Dict:
        return {
            'summary': 'Targeted optimization for {match_score}% match. Focus on closing specific skill gaps.',
            'structure_improvements': [
                'Create dedicated sections for missing skill categories',
                'Reorganize experience to highlight relevant projects'
            ],
            'ats_optimizations': [
                'Optimize for specific missing keywords',
                'Ensure keyword density is appropriate'
            ],
            'industry_specific': [
                'Research company-specific terminology',
                'Include relevant industry certifications'
            ],
            'quick_wins': [
                'Address lowest-scoring skill categories first',
                'Focus on high-priority missing keywords',
                'Tailor experience descriptions to job requirements'
            ]
        }
    
    def check_openai_status(self) -> Dict:
        """
        Check if OpenAI API is available and working
        """
        return {
            'available': self.openai_available,
            'api_key_configured': bool(os.getenv('OPENAI_API_KEY')),
            'library_installed': OPENAI_AVAILABLE
        }

    def _detect_seniority_level(self, analysis_data: Dict) -> str:
        """Detect seniority level from resume content"""
        resume_text = analysis_data['resume']['text_excerpt'].lower()

        senior_indicators = ['senior', 'lead', 'principal', 'architect', 'manager', 'director']
        mid_indicators = ['developer', 'engineer', 'analyst', 'specialist']
        junior_indicators = ['junior', 'associate', 'intern', 'entry', 'graduate']

        if any(indicator in resume_text for indicator in senior_indicators):
            return 'Senior Level'
        elif any(indicator in resume_text for indicator in junior_indicators):
            return 'Entry Level'
        else:
            return 'Mid Level'

    def _assess_ats_compatibility(self, analysis_data: Dict) -> str:
        """Assess ATS compatibility based on content analysis"""
        resume_text = analysis_data['resume']['text_excerpt']

        # Simple heuristics for ATS compatibility
        has_bullet_points = '•' in resume_text or '*' in resume_text
        has_clear_sections = any(section in resume_text.lower() for section in ['experience', 'education', 'skills'])
        keyword_density = len(analysis_data['resume']['text_excerpt'].split()) / max(analysis_data['resume']['keyword_count'], 1)

        if has_bullet_points and has_clear_sections and keyword_density < 50:
            return 'Good'
        elif has_clear_sections:
            return 'Fair'
        else:
            return 'Needs Improvement'

    def _calculate_keyword_density(self, analysis_data: Dict) -> str:
        """Calculate keyword density ratio"""
        total_words = len(analysis_data['resume']['text_excerpt'].split())
        keyword_count = analysis_data['resume']['keyword_count']

        if total_words > 0:
            density = (keyword_count / total_words) * 100
            if density > 3:
                return f'High ({density:.1f}%)'
            elif density > 1.5:
                return f'Optimal ({density:.1f}%)'
            else:
                return f'Low ({density:.1f}%)'
        return 'Unknown'

    def _count_quantified_achievements(self, analysis_data: Dict) -> str:
        """Count quantified achievements in resume"""
        resume_text = analysis_data['resume']['text_excerpt']

        # Look for numbers, percentages, dollar signs
        import re
        numbers = len(re.findall(r'\d+', resume_text))
        percentages = len(re.findall(r'\d+%', resume_text))
        currency = len(re.findall(r'\$\d+', resume_text))

        total_quantified = numbers + percentages + currency

        if total_quantified > 10:
            return f'Excellent ({total_quantified} metrics found)'
        elif total_quantified > 5:
            return f'Good ({total_quantified} metrics found)'
        elif total_quantified > 2:
            return f'Fair ({total_quantified} metrics found)'
        else:
            return f'Poor ({total_quantified} metrics found)'
