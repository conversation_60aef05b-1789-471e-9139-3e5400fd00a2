"""
US-07: Basic Suggestions Service
Identifies missing keywords and provides basic recommendations using local logic
"""

import logging
from typing import Dict, List, Set, Tuple
from models import db, Resume, JobDescription, MatchScore
from collections import Counter
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SuggestionsService:
    """
    Service for generating basic keyword suggestions and recommendations
    """
    
    def __init__(self):
        self.logger = logger
        
        # Priority keywords by category (local logic)
        self.priority_technical_skills = {
            'python', 'javascript', 'java', 'react', 'node.js', 'sql', 'postgresql', 
            'mongodb', 'docker', 'kubernetes', 'aws', 'azure', 'git', 'linux',
            'machine learning', 'data analysis', 'flask', 'django', 'fastapi',
            'html', 'css', 'typescript', 'angular', 'vue.js', 'express.js'
        }
        
        self.priority_soft_skills = {
            'communication', 'leadership', 'teamwork', 'problem-solving',
            'analytical thinking', 'creativity', 'adaptability', 'time management',
            'project management', 'collaboration', 'critical thinking'
        }
        
        # Common industry terms
        self.industry_keywords = {
            'agile', 'scrum', 'devops', 'ci/cd', 'testing', 'debugging',
            'optimization', 'scalability', 'security', 'performance',
            'architecture', 'design patterns', 'api', 'microservices'
        }
    
    def generate_basic_suggestions(self, resume_id: int, job_description_id: int, user_id: int) -> Dict:
        """
        Generate basic suggestions by identifying missing keywords and providing recommendations
        
        Args:
            resume_id: ID of the resume
            job_description_id: ID of the job description
            user_id: ID of the user (for security)
            
        Returns:
            Dict containing missing keywords and basic recommendations
        """
        try:
            # Get resume and job description
            resume = Resume.query.filter_by(
                id=resume_id,
                user_id=user_id,
                is_active=True
            ).first()
            
            job_description = JobDescription.query.filter_by(
                id=job_description_id,
                user_id=user_id,
                is_active=True
            ).first()
            
            if not resume:
                raise ValueError(f"Resume {resume_id} not found")
            
            if not job_description:
                raise ValueError(f"Job description {job_description_id} not found")
            
            if not resume.keywords_extracted:
                raise ValueError(f"Resume {resume_id} keywords not extracted yet")
            
            if not job_description.keywords_extracted:
                raise ValueError(f"Job description {job_description_id} keywords not extracted yet")
            
            # Get keywords
            resume_keywords = resume.get_keywords()
            jd_keywords = job_description.get_keywords()
            
            # Identify missing and matched keywords by category
            keyword_analysis = self._analyze_keywords(resume_keywords, jd_keywords)
            missing_keywords = keyword_analysis['missing']
            matched_keywords = keyword_analysis['matched']
            
            # Generate enhanced intelligent recommendations
            recommendations = self._generate_enhanced_recommendations(
                missing_keywords, resume_keywords, jd_keywords, resume, job_description
            )
            
            # Calculate priority scores
            priority_analysis = self._analyze_keyword_priority(missing_keywords)
            
            # Get match score if available
            match_score = MatchScore.query.filter_by(
                resume_id=resume_id,
                job_description_id=job_description_id,
                user_id=user_id,
                is_active=True
            ).first()
            
            self.logger.info(f"Generated basic suggestions for resume {resume_id} vs JD {job_description_id}")
            
            return {
                'success': True,
                'missing_keywords': missing_keywords,
                'matched_keywords': matched_keywords,
                'recommendations': recommendations,
                'priority_analysis': priority_analysis,
                'summary': {
                    'total_missing': sum(len(keywords) for keywords in missing_keywords.values()),
                    'high_priority_missing': len(priority_analysis['high_priority']),
                    'current_match_score': match_score.overall_score if match_score else None
                },
                'resume_info': {
                    'id': resume.id,
                    'filename': resume.original_filename,
                    'total_keywords': resume.keyword_count
                },
                'job_info': {
                    'id': job_description.id,
                    'title': job_description.title,
                    'company': job_description.company_name,
                    'total_keywords': job_description.keyword_count
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error generating basic suggestions: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _analyze_keywords(self, resume_keywords: Dict, jd_keywords: Dict) -> Dict:
        """
        Analyze keywords to identify matched, missing, and extra keywords
        """
        analysis = {
            'missing': {},
            'matched': {},
            'extra': {}
        }

        for category in ['technical_skills', 'soft_skills', 'other_keywords']:
            resume_set = set(keyword.lower().strip() for keyword in resume_keywords.get(category, []))
            jd_set = set(keyword.lower().strip() for keyword in jd_keywords.get(category, []))

            # Find matched, missing, and extra keywords
            matched_in_category = list(resume_set & jd_set)
            missing_in_category = list(jd_set - resume_set)
            extra_in_category = list(resume_set - jd_set)

            analysis['matched'][category] = sorted(matched_in_category)
            analysis['missing'][category] = sorted(missing_in_category)
            analysis['extra'][category] = sorted(extra_in_category)

        return analysis

    def _identify_missing_keywords(self, resume_keywords: Dict, jd_keywords: Dict) -> Dict:
        """
        Legacy method - kept for backward compatibility
        """
        return self._analyze_keywords(resume_keywords, jd_keywords)['missing']
    
    def _generate_basic_recommendations(self, missing_keywords: Dict, resume_keywords: Dict, jd_keywords: Dict) -> List[Dict]:
        """
        Generate basic recommendations based on missing keywords
        """
        recommendations = []
        
        # Technical skills recommendations
        if missing_keywords['technical_skills']:
            high_priority_tech = [
                kw for kw in missing_keywords['technical_skills'] 
                if kw in self.priority_technical_skills
            ]
            
            if high_priority_tech:
                recommendations.append({
                    'type': 'technical_skills',
                    'priority': 'high',
                    'title': 'Add High-Priority Technical Skills',
                    'description': f'Consider adding these in-demand technical skills: {", ".join(high_priority_tech[:5])}',
                    'keywords': high_priority_tech[:5],
                    'action': 'Add these skills to your resume if you have experience with them'
                })
            
            other_tech = [kw for kw in missing_keywords['technical_skills'] if kw not in high_priority_tech]
            if other_tech:
                recommendations.append({
                    'type': 'technical_skills',
                    'priority': 'medium',
                    'title': 'Additional Technical Skills',
                    'description': f'Job requires these technical skills: {", ".join(other_tech[:5])}',
                    'keywords': other_tech[:5],
                    'action': 'Consider learning these skills or highlighting related experience'
                })
        
        # Soft skills recommendations
        if missing_keywords['soft_skills']:
            priority_soft = [
                kw for kw in missing_keywords['soft_skills'] 
                if kw in self.priority_soft_skills
            ]
            
            if priority_soft:
                recommendations.append({
                    'type': 'soft_skills',
                    'priority': 'medium',
                    'title': 'Highlight Soft Skills',
                    'description': f'Emphasize these soft skills: {", ".join(priority_soft)}',
                    'keywords': priority_soft,
                    'action': 'Add examples demonstrating these soft skills in your experience section'
                })
        
        # Industry keywords recommendations
        if missing_keywords['other_keywords']:
            industry_terms = [
                kw for kw in missing_keywords['other_keywords'] 
                if kw in self.industry_keywords
            ]
            
            if industry_terms:
                recommendations.append({
                    'type': 'industry_keywords',
                    'priority': 'medium',
                    'title': 'Industry-Specific Terms',
                    'description': f'Include relevant industry terms: {", ".join(industry_terms[:5])}',
                    'keywords': industry_terms[:5],
                    'action': 'Incorporate these terms naturally into your job descriptions'
                })
        
        # General recommendations
        total_missing = sum(len(keywords) for keywords in missing_keywords.values())
        if total_missing > 10:
            recommendations.append({
                'type': 'general',
                'priority': 'high',
                'title': 'Significant Keyword Gap',
                'description': f'Your resume is missing {total_missing} keywords from the job description',
                'keywords': [],
                'action': 'Consider tailoring your resume more closely to this specific job posting'
            })
        elif total_missing > 5:
            recommendations.append({
                'type': 'general',
                'priority': 'medium',
                'title': 'Moderate Keyword Gap',
                'description': f'Adding {total_missing} relevant keywords could improve your match score',
                'keywords': [],
                'action': 'Focus on the high-priority missing keywords first'
            })
        else:
            recommendations.append({
                'type': 'general',
                'priority': 'low',
                'title': 'Good Keyword Coverage',
                'description': 'Your resume has good keyword coverage for this job',
                'keywords': [],
                'action': 'Fine-tune with the remaining missing keywords for optimal results'
            })
        
        return recommendations
    
    def _analyze_keyword_priority(self, missing_keywords: Dict) -> Dict:
        """
        Analyze and categorize missing keywords by priority
        """
        high_priority = []
        medium_priority = []
        low_priority = []
        
        # Technical skills priority
        for keyword in missing_keywords['technical_skills']:
            if keyword in self.priority_technical_skills:
                high_priority.append({
                    'keyword': keyword,
                    'category': 'technical_skills',
                    'reason': 'High-demand technical skill'
                })
            else:
                medium_priority.append({
                    'keyword': keyword,
                    'category': 'technical_skills',
                    'reason': 'Job-specific technical requirement'
                })
        
        # Soft skills priority
        for keyword in missing_keywords['soft_skills']:
            if keyword in self.priority_soft_skills:
                medium_priority.append({
                    'keyword': keyword,
                    'category': 'soft_skills',
                    'reason': 'Important soft skill'
                })
            else:
                low_priority.append({
                    'keyword': keyword,
                    'category': 'soft_skills',
                    'reason': 'Additional soft skill'
                })
        
        # Industry keywords priority
        for keyword in missing_keywords['other_keywords']:
            if keyword in self.industry_keywords:
                medium_priority.append({
                    'keyword': keyword,
                    'category': 'industry_keywords',
                    'reason': 'Industry-specific term'
                })
            else:
                low_priority.append({
                    'keyword': keyword,
                    'category': 'other_keywords',
                    'reason': 'General keyword'
                })
        
        return {
            'high_priority': high_priority,
            'medium_priority': medium_priority,
            'low_priority': low_priority,
            'total_by_priority': {
                'high': len(high_priority),
                'medium': len(medium_priority),
                'low': len(low_priority)
            }
        }
    
    def get_suggestion_history(self, user_id: int, limit: int = 10) -> List[Dict]:
        """
        Get recent suggestion requests for a user (based on match scores)
        """
        try:
            # Get recent match scores as proxy for suggestion history
            recent_matches = MatchScore.query.filter_by(
                user_id=user_id,
                is_active=True
            ).order_by(MatchScore.created_at.desc()).limit(limit).all()
            
            history = []
            for match in recent_matches:
                history.append({
                    'match_id': match.id,
                    'resume_title': match.resume.original_filename if match.resume else 'Unknown',
                    'job_title': match.job_description.title if match.job_description else 'Unknown',
                    'company_name': match.job_description.company_name if match.job_description else None,
                    'match_score': match.overall_score,
                    'created_at': match.created_at.isoformat(),
                    'can_generate_suggestions': True
                })
            
            return history
            
        except Exception as e:
            self.logger.error(f"Error fetching suggestion history: {e}")
            return []

    def _generate_enhanced_recommendations(self, missing_keywords: Dict, resume_keywords: Dict,
                                         jd_keywords: Dict, resume, job_description) -> List[Dict]:
        """
        Generate enhanced, actionable recommendations with specific placement suggestions
        """
        recommendations = []

        # Analyze job description for context
        jd_content = job_description.job_text.lower() if job_description.job_text else ''
        resume_content = resume.extracted_text.lower() if resume.extracted_text else ''

        # High-priority technical skills recommendations
        for keyword in missing_keywords.get('technical_skills', []):
            recommendation = self._create_technical_skill_recommendation(keyword, jd_content, resume_content)
            if recommendation:
                recommendations.append(recommendation)

        # Soft skills recommendations
        for keyword in missing_keywords.get('soft_skills', []):
            recommendation = self._create_soft_skill_recommendation(keyword, jd_content, resume_content)
            if recommendation:
                recommendations.append(recommendation)

        # Industry-specific recommendations
        for keyword in missing_keywords.get('industry_keywords', []):
            recommendation = self._create_industry_recommendation(keyword, jd_content, resume_content)
            if recommendation:
                recommendations.append(recommendation)

        # General keyword recommendations
        for keyword in missing_keywords.get('other_keywords', []):
            recommendation = self._create_general_recommendation(keyword, jd_content, resume_content)
            if recommendation:
                recommendations.append(recommendation)

        # Add structural recommendations
        structural_recommendations = self._generate_structural_recommendations(resume_content, jd_content)
        recommendations.extend(structural_recommendations)

        # Sort by priority and impact
        recommendations.sort(key=lambda x: (
            {'high': 3, 'medium': 2, 'low': 1}.get(x.get('priority', 'low'), 1),
            x.get('impact_score', 0)
        ), reverse=True)

        return recommendations[:15]  # Return top 15 most impactful recommendations

    def _create_technical_skill_recommendation(self, keyword: str, jd_content: str, resume_content: str) -> Dict:
        """Create specific recommendation for technical skills"""

        # Context-aware suggestions based on keyword
        skill_contexts = {
            'python': {
                'sections': ['Technical Skills', 'Programming Languages', 'Core Competencies'],
                'examples': ['Python (Django, Flask, FastAPI)', 'Python for data analysis', 'Python automation scripts'],
                'projects': 'Consider adding a Python project showcasing web development or data analysis'
            },
            'javascript': {
                'sections': ['Technical Skills', 'Frontend Development', 'Programming Languages'],
                'examples': ['JavaScript (ES6+, React, Node.js)', 'JavaScript for web development', 'Full-stack JavaScript'],
                'projects': 'Add a JavaScript project demonstrating frontend or full-stack capabilities'
            },
            'react': {
                'sections': ['Frontend Frameworks', 'Technical Skills', 'Web Development'],
                'examples': ['React.js with hooks and context', 'React with Redux/TypeScript', 'React Native for mobile'],
                'projects': 'Include a React project with modern features like hooks, context, or state management'
            },
            'aws': {
                'sections': ['Cloud Platforms', 'Technical Skills', 'DevOps'],
                'examples': ['AWS (EC2, S3, Lambda, RDS)', 'AWS cloud architecture', 'AWS DevOps tools'],
                'projects': 'Mention cloud deployment experience or AWS certifications'
            }
        }

        context = skill_contexts.get(keyword.lower(), {
            'sections': ['Technical Skills', 'Core Competencies'],
            'examples': [f'{keyword} development', f'{keyword} implementation'],
            'projects': f'Consider adding experience with {keyword}'
        })

        return {
            'type': 'technical_skill',
            'keyword': keyword,
            'priority': 'high',
            'impact_score': 9,
            'title': f'Add {keyword.title()} to Technical Skills',
            'description': f'The job requires {keyword} expertise. This is a critical technical requirement.',
            'action_items': [
                f"Add '{keyword}' to your {context['sections'][0]} section",
                f"Use specific examples: {context['examples'][0]}",
                f"Quantify your experience: 'X years of {keyword} development'",
                context['projects']
            ],
            'placement_suggestions': context['sections'],
            'example_phrases': context['examples'],
            'urgency': 'immediate'
        }

    def _create_soft_skill_recommendation(self, keyword: str, jd_content: str, resume_content: str) -> Dict:
        """Create specific recommendation for soft skills"""

        soft_skill_contexts = {
            'leadership': {
                'sections': ['Professional Experience', 'Key Achievements', 'Summary'],
                'examples': ['Led team of X developers', 'Managed cross-functional projects', 'Mentored junior staff'],
                'action': 'Demonstrate leadership through specific examples and metrics'
            },
            'communication': {
                'sections': ['Professional Experience', 'Key Skills', 'Summary'],
                'examples': ['Presented to stakeholders', 'Collaborated with cross-functional teams', 'Documented technical processes'],
                'action': 'Show communication skills through collaboration and presentation examples'
            },
            'problem-solving': {
                'sections': ['Professional Experience', 'Key Achievements', 'Projects'],
                'examples': ['Resolved critical system issues', 'Optimized performance by X%', 'Debugged complex problems'],
                'action': 'Highlight specific problems solved and their business impact'
            }
        }

        context = soft_skill_contexts.get(keyword.lower(), {
            'sections': ['Professional Experience', 'Key Skills'],
            'examples': [f'Demonstrated {keyword} in project delivery', f'Applied {keyword} to achieve results'],
            'action': f'Showcase {keyword} through specific examples'
        })

        return {
            'type': 'soft_skill',
            'keyword': keyword,
            'priority': 'medium',
            'impact_score': 7,
            'title': f'Demonstrate {keyword.title()} Skills',
            'description': f'The role emphasizes {keyword}. Show this through concrete examples.',
            'action_items': [
                f"Add {keyword} examples to your {context['sections'][0]} section",
                f"Use action verbs: {context['examples'][0]}",
                "Quantify the impact where possible",
                context['action']
            ],
            'placement_suggestions': context['sections'],
            'example_phrases': context['examples'],
            'urgency': 'moderate'
        }

    def _create_industry_recommendation(self, keyword: str, jd_content: str, resume_content: str) -> Dict:
        """Create specific recommendation for industry keywords"""

        return {
            'type': 'industry_keyword',
            'keyword': keyword,
            'priority': 'medium',
            'impact_score': 6,
            'title': f'Include {keyword.title()} Experience',
            'description': f'The role requires familiarity with {keyword}. This is industry-specific terminology.',
            'action_items': [
                f"Add '{keyword}' to relevant project descriptions",
                f"Mention {keyword} in context of your experience",
                f"Use {keyword} in your technical skills or methodologies section",
                f"Provide specific examples of {keyword} implementation"
            ],
            'placement_suggestions': ['Professional Experience', 'Technical Skills', 'Projects'],
            'example_phrases': [f'{keyword} implementation', f'{keyword} methodology', f'{keyword} best practices'],
            'urgency': 'moderate'
        }

    def _create_general_recommendation(self, keyword: str, jd_content: str, resume_content: str) -> Dict:
        """Create recommendation for general keywords"""

        return {
            'type': 'general_keyword',
            'keyword': keyword,
            'priority': 'low',
            'impact_score': 4,
            'title': f'Consider Adding {keyword.title()}',
            'description': f'The job description mentions {keyword}. Consider if this applies to your experience.',
            'action_items': [
                f"Review if you have {keyword} experience",
                f"Add {keyword} if relevant to your background",
                f"Use {keyword} in appropriate context",
                "Ensure natural integration into existing content"
            ],
            'placement_suggestions': ['Professional Experience', 'Skills', 'Summary'],
            'example_phrases': [f'{keyword} experience', f'{keyword} knowledge', f'{keyword} familiarity'],
            'urgency': 'low'
        }

    def _generate_structural_recommendations(self, resume_content: str, jd_content: str) -> List[Dict]:
        """Generate recommendations for resume structure and formatting"""

        recommendations = []

        # Check for quantified achievements
        if not any(char.isdigit() for char in resume_content):
            recommendations.append({
                'type': 'structure',
                'keyword': 'quantified_achievements',
                'priority': 'high',
                'impact_score': 8,
                'title': 'Add Quantified Achievements',
                'description': 'Your resume lacks specific numbers and metrics. Quantified achievements are crucial.',
                'action_items': [
                    'Add specific numbers: "Improved performance by 25%"',
                    'Include team sizes: "Led team of 5 developers"',
                    'Mention timeframes: "Delivered project 2 weeks ahead of schedule"',
                    'Use percentages, dollar amounts, or other metrics where possible'
                ],
                'placement_suggestions': ['Professional Experience', 'Key Achievements'],
                'example_phrases': ['Increased efficiency by X%', 'Managed budget of $X', 'Led team of X people'],
                'urgency': 'immediate'
            })

        # Check for action verbs
        weak_verbs = ['responsible for', 'worked on', 'helped with', 'involved in']
        if any(verb in resume_content for verb in weak_verbs):
            recommendations.append({
                'type': 'structure',
                'keyword': 'action_verbs',
                'priority': 'medium',
                'impact_score': 7,
                'title': 'Use Strong Action Verbs',
                'description': 'Replace weak phrases with strong action verbs to show impact.',
                'action_items': [
                    'Replace "responsible for" with "managed", "led", "oversaw"',
                    'Change "worked on" to "developed", "implemented", "created"',
                    'Use "achieved", "delivered", "optimized" instead of "helped with"',
                    'Start each bullet point with a strong action verb'
                ],
                'placement_suggestions': ['Professional Experience', 'Projects'],
                'example_phrases': ['Developed', 'Implemented', 'Optimized', 'Delivered', 'Achieved'],
                'urgency': 'moderate'
            })

        # Check for relevant sections
        if 'project' not in resume_content.lower():
            recommendations.append({
                'type': 'structure',
                'keyword': 'projects_section',
                'priority': 'medium',
                'impact_score': 6,
                'title': 'Add Projects Section',
                'description': 'Consider adding a Projects section to showcase relevant work.',
                'action_items': [
                    'Create a "Projects" or "Key Projects" section',
                    'Include 2-3 most relevant projects',
                    'Describe technologies used and outcomes achieved',
                    'Link projects to job requirements where possible'
                ],
                'placement_suggestions': ['After Professional Experience', 'Before Education'],
                'example_phrases': ['Key Projects', 'Notable Projects', 'Technical Projects'],
                'urgency': 'moderate'
            })

        return recommendations
