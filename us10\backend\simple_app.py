#!/usr/bin/env python3
"""
Simple Flask app to test basic functionality
"""

from flask import Flask, jsonify
import os

# Create simple Flask app
app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <h1>🩺 Dr. Resume - Health Check</h1>
    <p>✅ Flask app is running successfully!</p>
    <p>🔗 <a href="/health">Check API Health</a></p>
    <p>🔗 <a href="/test-ai">Test Local AI</a></p>
    '''

@app.route('/health')
def health():
    return jsonify({
        'status': 'healthy',
        'message': 'Dr. Resume API is running',
        'version': '1.0.0'
    })

@app.route('/test-ai')
def test_ai():
    try:
        from services.local_ai_suggestions_service import LocalAISuggestionsService
        ai_service = LocalAISuggestionsService()
        
        # Test with sample data
        sample_resume_keywords = {
            'technical_skills': ['python', 'javascript', 'html'],
            'soft_skills': ['communication', 'teamwork'],
            'other_keywords': ['project management']
        }
        
        sample_jd_keywords = {
            'technical_skills': ['python', 'react', 'sql'],
            'soft_skills': ['communication', 'leadership'],
            'other_keywords': ['agile', 'scrum']
        }
        
        result = ai_service.generate_local_ai_suggestions(
            resume_id=1,
            job_description_id=1,
            resume_keywords=sample_resume_keywords,
            jd_keywords=sample_jd_keywords,
            resume_text="Sample resume text",
            jd_text="Sample job description text"
        )
        
        return jsonify({
            'status': 'success',
            'message': 'Local AI is working!',
            'sample_suggestions': {
                'summary': result.get('analysis_summary', 'AI analysis completed'),
                'keyword_count': len(result.get('keyword_recommendations', [])),
                'ai_powered': result.get('ai_powered', False),
                'local_processing': result.get('local_processing', False)
            }
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Local AI test failed: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("🚀 Starting simple Dr. Resume app...")
    print("📍 Available at: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
