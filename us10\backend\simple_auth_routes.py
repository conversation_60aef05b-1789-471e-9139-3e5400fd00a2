"""
Simple Authentication Routes without complex middleware
For testing and debugging
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from models import db, User
from werkzeug.security import generate_password_hash, check_password_hash
import re

# Create blueprint
simple_auth_bp = Blueprint('simple_auth', __name__, url_prefix='/api')

@simple_auth_bp.route('/simple-register', methods=['POST'])
def simple_register():
    """Simple registration without middleware"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400
        
        first_name = data.get('first_name', '').strip()
        last_name = data.get('last_name', '').strip()
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')
        
        # Basic validation
        if not all([first_name, last_name, email, password]):
            return jsonify({'success': False, 'message': 'All fields are required'}), 400
        
        # Check if user exists
        if User.query.filter_by(email=email).first():
            return jsonify({'success': False, 'message': 'Email already registered'}), 400
        
        # Create user
        user = User(
            first_name=first_name,
            last_name=last_name,
            email=email,
            password=password
        )
        
        db.session.add(user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Registration successful',
            'user': {
                'id': user.id,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'email': user.email
            }
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'Registration failed: {str(e)}'}), 500

@simple_auth_bp.route('/simple-login', methods=['POST'])
def simple_login():
    """Simple login without middleware"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400
        
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')
        
        if not email or not password:
            return jsonify({'success': False, 'message': 'Email and password required'}), 400
        
        # Find user
        user = User.query.filter_by(email=email).first()
        
        if not user or not user.check_password(password):
            return jsonify({'success': False, 'message': 'Invalid email or password'}), 401
        
        # Create token
        access_token = create_access_token(identity=user.id)
        
        return jsonify({
            'success': True,
            'message': 'Login successful',
            'user': {
                'id': user.id,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'email': user.email
            },
            'tokens': {
                'access_token': access_token
            }
        }), 200
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'Login failed: {str(e)}'}), 500

@simple_auth_bp.route('/simple-basic-suggestions', methods=['POST'])
@jwt_required()
def simple_basic_suggestions():
    """Simple basic suggestions with JWT auth"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'success': False, 'message': 'User not found'}), 404
        
        data = request.get_json()
        resume_id = data.get('resume_id')
        job_description_id = data.get('job_description_id')
        
        if not resume_id or not job_description_id:
            return jsonify({'success': False, 'message': 'Resume ID and Job Description ID required'}), 400
        
        # For now, return mock suggestions
        mock_suggestions = {
            'success': True,
            'suggestions': {
                'recommendations': [
                    "✅ Strong foundation! Your Python experience is exactly what they're looking for.",
                    "🚨 Critical Gap: React is essential for this role. Consider adding projects to demonstrate this skill.",
                    "💡 Since you know Python, learning Django would be a natural next step.",
                    "✅ Excellent! You already have Communication experience. Make sure to highlight this prominently.",
                    "🎯 Consider adding SQL to your skillset - it's mentioned in the job requirements.",
                    "💼 Your Agile experience is valuable - make sure it's prominently featured."
                ],
                'missing_keywords': {
                    'technical_skills': ['react', 'sql'],
                    'soft_skills': ['leadership'],
                    'other_keywords': ['scrum']
                },
                'matched_keywords': {
                    'technical_skills': ['python'],
                    'soft_skills': ['communication'],
                    'other_keywords': ['agile']
                },
                'priority_analysis': {
                    'critical_gaps': ['react', 'sql'],
                    'important_gaps': ['leadership', 'scrum'],
                    'strengths': ['python', 'communication', 'agile']
                }
            }
        }
        
        return jsonify(mock_suggestions), 200
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500
