#!/usr/bin/env python3
"""
Complete Flow Test for Dr. Resume US-10
Tests the entire user journey from registration to AI suggestions
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:5000"

def test_registration():
    """Test user registration"""
    print("👤 Testing User Registration...")
    
    url = f"{BASE_URL}/api/register"
    data = {
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON>",
        "email": "<EMAIL>",
        "password": "password123",
        "confirm_password": "password123"
    }
    
    try:
        response = requests.post(url, json=data)
        result = response.json()
        
        if response.status_code == 200 and result.get('success'):
            print("✅ Registration successful")
            return True
        elif response.status_code == 400 and 'already registered' in result.get('message', ''):
            print("✅ User already exists (continuing with login)")
            return True
        else:
            print(f"❌ Registration failed: {result.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return False

def test_login():
    """Test user login and get token"""
    print("🔐 Testing User Login...")
    
    url = f"{BASE_URL}/api/login"
    data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(url, json=data)
        result = response.json()
        
        if response.status_code == 200 and result.get('success'):
            token = result['tokens']['access_token']
            user_info = result['user']
            print(f"✅ Login successful - User: {user_info['first_name']} {user_info['last_name']}")
            return token
        else:
            print(f"❌ Login failed: {result.get('message')}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_health_check():
    """Test health endpoint"""
    print("🩺 Testing Health Check...")
    
    try:
        response = requests.get(f"{BASE_URL}/health")
        result = response.json()
        
        if response.status_code == 200 and result.get('status') == 'healthy':
            print("✅ Health check passed")
            return True
        else:
            print("❌ Health check failed")
            return False
            
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_suggestions_without_auth():
    """Test suggestions endpoints without authentication"""
    print("🤖 Testing Suggestions (No Auth)...")
    
    try:
        # Test Local AI
        response = requests.get(f"{BASE_URL}/api/test-suggestions")
        result = response.json()
        
        if response.status_code == 200 and result.get('success'):
            print("✅ Local AI suggestions working")
            suggestions = result['suggestions']
            print(f"  📊 Generated {len(suggestions.get('keyword_recommendations', []))} recommendations")
        else:
            print("❌ Local AI suggestions failed")
            return False
        
        # Test Basic Suggestions
        response = requests.get(f"{BASE_URL}/api/test-basic-suggestions")
        result = response.json()
        
        if response.status_code == 200 and result.get('success'):
            print("✅ Basic suggestions working")
            suggestions = result['suggestions']
            print(f"  💡 Generated {len(suggestions.get('recommendations', []))} basic recommendations")
            return True
        else:
            print("❌ Basic suggestions failed")
            return False
            
    except Exception as e:
        print(f"❌ Suggestions test error: {e}")
        return False

def test_frontend_pages():
    """Test frontend page accessibility"""
    print("🌐 Testing Frontend Pages...")
    
    pages = [
        ('/', 'Landing Page'),
        ('/login', 'Login Page'),
        ('/register', 'Register Page'),
        ('/dashboard', 'Dashboard'),
        ('/suggestions', 'Suggestions Page'),
        ('/upload', 'Upload Page'),
        ('/job-descriptions', 'Job Descriptions'),
        ('/matching', 'Matching Page'),
        ('/account', 'Account Page')
    ]
    
    success_count = 0
    
    for path, name in pages:
        try:
            response = requests.get(f"{BASE_URL}{path}")
            if response.status_code == 200:
                print(f"  ✅ {name}")
                success_count += 1
            else:
                print(f"  ❌ {name} - Status: {response.status_code}")
        except Exception as e:
            print(f"  ❌ {name} - Error: {e}")
    
    print(f"📊 Frontend Pages: {success_count}/{len(pages)} accessible")
    return success_count == len(pages)

def main():
    """Run complete flow test"""
    print("🩺" + "="*60 + "🩺")
    print("🧪 Dr. Resume Complete Flow Test")
    print("="*62)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 Testing: {BASE_URL}")
    print()
    
    tests = [
        ("Health Check", test_health_check),
        ("Frontend Pages", test_frontend_pages),
        ("User Registration", test_registration),
        ("User Login", test_login),
        ("Suggestions (No Auth)", test_suggestions_without_auth)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"🔍 {test_name}:")
        try:
            if test_name == "User Login":
                # Special handling for login to get token
                token = test_func()
                results[test_name] = token is not None
                if token:
                    print(f"  🎫 Token: {token[:20]}...")
            else:
                results[test_name] = test_func()
        except Exception as e:
            print(f"  ❌ Test failed: {e}")
            results[test_name] = False
        print()
    
    # Summary
    print("="*62)
    print("📊 TEST SUMMARY")
    print("="*62)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:25} {status}")
    
    print()
    print(f"Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("🚀 Dr. Resume is fully functional!")
        print()
        print("💡 Next Steps:")
        print("1. Open browser: http://localhost:5000")
        print("2. Register/Login with: <EMAIL> / password123")
        print("3. Upload a resume")
        print("4. Add a job description")
        print("5. Generate AI suggestions!")
    else:
        print("⚠️ Some tests failed")
        print("🔧 Please check the failing components")
    
    print("🩺" + "="*60 + "🩺")

if __name__ == '__main__':
    main()
