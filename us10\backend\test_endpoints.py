#!/usr/bin/env python3
"""
Test All Endpoints for Dr. Resume US-10
Tests the specific endpoints that are failing
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def get_auth_token():
    """Get authentication token"""
    print("🔐 Getting authentication token...")
    
    # First register a user
    register_data = {
        "first_name": "Test",
        "last_name": "User",
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/simple-register", json=register_data)
        print(f"Registration: {response.status_code}")
    except:
        pass  # User might already exist
    
    # Login to get token
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/simple-login", json=login_data)
        if response.status_code == 200:
            result = response.json()
            token = result['tokens']['access_token']
            print(f"✅ Token obtained: {token[:20]}...")
            return token
        else:
            print(f"❌ Login failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_job_description_endpoint(token):
    """Test job description upload endpoint"""
    print("\n💼 Testing Job Description Endpoint...")
    
    if not token:
        print("❌ No token available")
        return False
    
    url = f"{BASE_URL}/api/upload_jd"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    data = {
        "title": "Software Developer",
        "company_name": "Test Company",
        "job_text": "We are looking for a Python developer with React experience. Must have strong communication skills and experience with agile methodologies."
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        result = response.json()
        
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if response.status_code == 200 and result.get('success'):
            print("✅ Job description endpoint working")
            return True
        else:
            print("❌ Job description endpoint failed")
            return False
            
    except Exception as e:
        print(f"❌ Job description error: {e}")
        return False

def test_upload_endpoint(token):
    """Test resume upload endpoint"""
    print("\n📤 Testing Resume Upload Endpoint...")
    
    if not token:
        print("❌ No token available")
        return False
    
    # Test if upload endpoint exists
    url = f"{BASE_URL}/api/upload_resume"
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    # Create a simple test file
    files = {
        'resume': ('test_resume.txt', 'This is a test resume with Python and JavaScript skills.', 'text/plain')
    }
    
    try:
        response = requests.post(url, files=files, headers=headers)
        result = response.json() if response.headers.get('content-type', '').startswith('application/json') else {'message': response.text}
        
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if response.status_code == 200:
            print("✅ Upload endpoint working")
            return True
        else:
            print("❌ Upload endpoint failed")
            return False
            
    except Exception as e:
        print(f"❌ Upload error: {e}")
        return False

def test_history_endpoint(token):
    """Test history/scan endpoint"""
    print("\n📊 Testing History Endpoint...")
    
    if not token:
        print("❌ No token available")
        return False
    
    url = f"{BASE_URL}/api/scan_history"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        result = response.json() if response.headers.get('content-type', '').startswith('application/json') else {'message': response.text}
        
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if response.status_code == 200:
            print("✅ History endpoint working")
            return True
        else:
            print("❌ History endpoint failed")
            return False
            
    except Exception as e:
        print(f"❌ History error: {e}")
        return False

def test_account_endpoint(token):
    """Test account information endpoint"""
    print("\n👤 Testing Account Endpoint...")
    
    if not token:
        print("❌ No token available")
        return False
    
    url = f"{BASE_URL}/api/account"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        result = response.json() if response.headers.get('content-type', '').startswith('application/json') else {'message': response.text}
        
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if response.status_code == 200:
            print("✅ Account endpoint working")
            return True
        else:
            print("❌ Account endpoint failed")
            return False
            
    except Exception as e:
        print(f"❌ Account error: {e}")
        return False

def test_basic_suggestions_endpoint(token):
    """Test basic suggestions endpoint"""
    print("\n💡 Testing Basic Suggestions Endpoint...")
    
    if not token:
        print("❌ No token available")
        return False
    
    url = f"{BASE_URL}/api/basic_suggestions"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    data = {
        "resume_id": 1,
        "job_description_id": 1
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        result = response.json() if response.headers.get('content-type', '').startswith('application/json') else {'message': response.text}
        
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if response.status_code == 200:
            print("✅ Basic suggestions endpoint working")
            return True
        else:
            print("❌ Basic suggestions endpoint failed")
            return False
            
    except Exception as e:
        print(f"❌ Basic suggestions error: {e}")
        return False

def main():
    """Test all failing endpoints"""
    print("🧪 Testing All Failing Endpoints")
    print("="*50)
    
    # Get authentication token
    token = get_auth_token()
    
    if not token:
        print("❌ Cannot proceed without authentication token")
        return
    
    tests = [
        ("Job Description", lambda: test_job_description_endpoint(token)),
        ("Resume Upload", lambda: test_upload_endpoint(token)),
        ("History/Scan", lambda: test_history_endpoint(token)),
        ("Account Info", lambda: test_account_endpoint(token)),
        ("Basic Suggestions", lambda: test_basic_suggestions_endpoint(token))
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test failed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*50)
    print("📊 ENDPOINT TEST SUMMARY")
    print("="*50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} endpoints working")
    
    if passed == total:
        print("🎉 ALL ENDPOINTS WORKING!")
    else:
        print("⚠️ Some endpoints need fixing")

if __name__ == '__main__':
    main()
