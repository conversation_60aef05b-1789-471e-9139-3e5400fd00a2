#!/usr/bin/env python3
"""
Test Fixed Endpoints for Dr. Resume US-10
Tests all the endpoints that should now be working
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def get_auth_token():
    """Get authentication token"""
    print("🔐 Getting authentication token...")
    
    # Login to get token
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/simple-login", json=login_data)
        if response.status_code == 200:
            result = response.json()
            token = result['tokens']['access_token']
            print(f"✅ Token obtained: {token[:20]}...")
            return token
        else:
            print(f"❌ Login failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_job_description_fixed(token):
    """Test job description endpoint (fixed)"""
    print("\n💼 Testing Job Description Endpoint (Fixed)...")
    
    url = f"{BASE_URL}/api/upload_jd"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    data = {
        "title": "Senior Python Developer",
        "company_name": "Tech Corp",
        "job_text": "We need a senior Python developer with Django, React, and PostgreSQL experience. Strong communication and leadership skills required."
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        result = response.json()
        
        print(f"Status: {response.status_code}")
        
        # Accept both 200 and 201 as success
        if response.status_code in [200, 201] and result.get('success'):
            print("✅ Job description endpoint working")
            return True, result.get('job_description', {}).get('id')
        else:
            print("❌ Job description endpoint failed")
            return False, None
            
    except Exception as e:
        print(f"❌ Job description error: {e}")
        return False, None

def test_upload_fixed(token):
    """Test upload endpoint (create test data)"""
    print("\n📤 Testing Upload Endpoint (Create Test Data)...")
    
    url = f"{BASE_URL}/api/test-upload"
    
    try:
        response = requests.post(url)
        result = response.json()
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200 and result.get('success'):
            print("✅ Upload test data created")
            return True, result.get('resume', {}).get('id')
        else:
            print("❌ Upload test data failed")
            return False, None
            
    except Exception as e:
        print(f"❌ Upload error: {e}")
        return False, None

def test_history_fixed(token):
    """Test history endpoint (fixed)"""
    print("\n📊 Testing History Endpoint (Fixed)...")
    
    url = f"{BASE_URL}/api/scan_history"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        result = response.json()
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200 and result.get('success'):
            print(f"✅ History endpoint working - {result.get('total', 0)} records")
            return True
        else:
            print("❌ History endpoint failed")
            return False
            
    except Exception as e:
        print(f"❌ History error: {e}")
        return False

def test_account_fixed(token):
    """Test account endpoint (fixed)"""
    print("\n👤 Testing Account Endpoint (Fixed)...")
    
    url = f"{BASE_URL}/api/account"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        result = response.json()
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200 and result.get('success'):
            user = result.get('user', {})
            print(f"✅ Account endpoint working - {user.get('first_name')} {user.get('last_name')}")
            return True
        else:
            print("❌ Account endpoint failed")
            return False
            
    except Exception as e:
        print(f"❌ Account error: {e}")
        return False

def test_suggestions_fixed(token, resume_id=None, jd_id=None):
    """Test suggestions endpoint (fixed)"""
    print("\n💡 Testing Suggestions Endpoint (Fixed)...")
    
    url = f"{BASE_URL}/api/test-full-suggestions"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, headers=headers)
        result = response.json()
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200 and result.get('success'):
            suggestions = result.get('suggestions', {})
            recommendations = suggestions.get('recommendations', [])
            print(f"✅ Suggestions endpoint working - {len(recommendations)} recommendations")
            return True
        else:
            print("❌ Suggestions endpoint failed")
            return False
            
    except Exception as e:
        print(f"❌ Suggestions error: {e}")
        return False

def test_password_change_fixed(token):
    """Test password change endpoint (fixed)"""
    print("\n🔒 Testing Password Change Endpoint (Fixed)...")
    
    url = f"{BASE_URL}/api/change_password"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    data = {
        "current_password": "password123",
        "new_password": "newpassword123"
    }
    
    try:
        response = requests.put(url, json=data, headers=headers)
        result = response.json()
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200 and result.get('success'):
            print("✅ Password change endpoint working")
            
            # Change it back
            data_back = {
                "current_password": "newpassword123",
                "new_password": "password123"
            }
            requests.put(url, json=data_back, headers=headers)
            
            return True
        else:
            print("❌ Password change endpoint failed")
            return False
            
    except Exception as e:
        print(f"❌ Password change error: {e}")
        return False

def main():
    """Test all fixed endpoints"""
    print("🧪 Testing All Fixed Endpoints")
    print("="*50)
    
    # Get authentication token
    token = get_auth_token()
    
    if not token:
        print("❌ Cannot proceed without authentication token")
        return
    
    tests = [
        ("Job Description", lambda: test_job_description_fixed(token)[0]),
        ("Upload Test Data", lambda: test_upload_fixed(token)[0]),
        ("History/Scan", lambda: test_history_fixed(token)),
        ("Account Info", lambda: test_account_fixed(token)),
        ("Suggestions", lambda: test_suggestions_fixed(token)),
        ("Password Change", lambda: test_password_change_fixed(token))
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test failed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*50)
    print("📊 FIXED ENDPOINTS TEST SUMMARY")
    print("="*50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} endpoints working")
    
    if passed == total:
        print("🎉 ALL ENDPOINTS FIXED AND WORKING!")
        print("🚀 Dr. Resume is fully functional!")
    elif passed >= 4:
        print("🎯 Most endpoints working - great progress!")
    else:
        print("⚠️ Some endpoints still need fixing")

if __name__ == '__main__':
    main()
