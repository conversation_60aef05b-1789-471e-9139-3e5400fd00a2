#!/usr/bin/env python3
"""
OpenAI Configuration Test Script
Tests if OpenAI API is properly configured for premium suggestions
"""

import os
from dotenv import load_dotenv

def test_openai_config():
    """Test OpenAI configuration"""
    print("🔍 Testing OpenAI Configuration...")
    print("="*50)
    
    # Load environment variables
    load_dotenv()
    
    # Check if OpenAI API key is set
    api_key = os.getenv('OPENAI_API_KEY')
    
    if not api_key or api_key == 'your-openai-api-key-here':
        print("❌ OpenAI API Key not configured")
        print()
        print("📋 To get REAL AI-powered suggestions:")
        print("1. Go to: https://platform.openai.com/api-keys")
        print("2. Create an account or sign in")
        print("3. Click 'Create new secret key'")
        print("4. Copy the API key")
        print("5. Edit us10/backend/.env file")
        print("6. Replace 'your-openai-api-key-here' with your actual API key")
        print()
        print("💡 Current Status: Using FALLBACK suggestions (not real AI)")
        print("   - Fallback suggestions are pre-written templates")
        print("   - They provide good advice but are not personalized")
        print("   - Real AI suggestions analyze your specific resume vs job description")
        return False
    else:
        print("✅ OpenAI API Key found")
        print(f"   Key: {api_key[:8]}...{api_key[-4:] if len(api_key) > 12 else '***'}")
        
        # Test if OpenAI library is available
        try:
            from openai import OpenAI
            client = OpenAI(api_key=api_key)
            print("✅ OpenAI library imported successfully")
            
            # Test API connection (optional - costs money)
            print("💡 API key configured - premium suggestions will use real AI")
            return True
            
        except ImportError:
            print("❌ OpenAI library not installed")
            print("   Run: pip install openai")
            return False
        except Exception as e:
            print(f"⚠️  API key configured but may be invalid: {e}")
            return False

def main():
    """Main test function"""
    print("🩺" + "="*48 + "🩺")
    print("🤖 Dr. Resume OpenAI Configuration Test")
    print("="*50)
    print()
    
    success = test_openai_config()
    
    print()
    print("="*50)
    if success:
        print("🎉 OpenAI is properly configured!")
        print("🚀 Premium suggestions will use real AI")
    else:
        print("⚠️  OpenAI not configured - using fallback suggestions")
        print("📖 Follow the instructions above to enable real AI")
    print("🩺" + "="*48 + "🩺")

if __name__ == '__main__':
    main()
