#!/usr/bin/env python3
"""
Test Simple Authentication
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_simple_register():
    """Test simple registration"""
    print("👤 Testing Simple Registration...")
    
    url = f"{BASE_URL}/api/simple-register"
    data = {
        "first_name": "<PERSON>",
        "last_name": "<PERSON>",
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(url, json=data)
        result = response.json()
        
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if response.status_code in [200, 201] and result.get('success'):
            print("✅ Simple registration successful")
            return True
        elif response.status_code == 400 and 'already registered' in result.get('message', ''):
            print("✅ User already exists (continuing)")
            return True
        else:
            print(f"❌ Simple registration failed")
            return False
            
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return False

def test_simple_login():
    """Test simple login"""
    print("\n🔐 Testing Simple Login...")
    
    url = f"{BASE_URL}/api/simple-login"
    data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(url, json=data)
        result = response.json()
        
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if response.status_code == 200 and result.get('success'):
            token = result['tokens']['access_token']
            print("✅ Simple login successful")
            print(f"🎫 Token: {token[:30]}...")
            return token
        else:
            print(f"❌ Simple login failed")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_simple_suggestions(token):
    """Test simple suggestions with token"""
    print("\n💡 Testing Simple Suggestions...")
    
    url = f"{BASE_URL}/api/simple-basic-suggestions"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    data = {
        "resume_id": 1,
        "job_description_id": 1
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        result = response.json()
        
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(result, indent=2)}")
        
        if response.status_code == 200 and result.get('success'):
            suggestions = result['suggestions']
            print("✅ Simple suggestions successful")
            print(f"💡 Generated {len(suggestions.get('recommendations', []))} recommendations")
            return True
        else:
            print(f"❌ Simple suggestions failed")
            return False
            
    except Exception as e:
        print(f"❌ Suggestions error: {e}")
        return False

def main():
    """Test simple authentication flow"""
    print("🧪 Testing Simple Authentication Flow")
    print("="*50)
    
    # Test registration
    if not test_simple_register():
        print("❌ Registration failed, stopping tests")
        return
    
    # Test login
    token = test_simple_login()
    if not token:
        print("❌ Login failed, stopping tests")
        return
    
    # Test suggestions
    if test_simple_suggestions(token):
        print("\n🎉 ALL SIMPLE TESTS PASSED!")
        print("✅ Authentication working")
        print("✅ JWT tokens working")
        print("✅ Protected endpoints working")
        print("✅ Suggestions working")
    else:
        print("\n❌ Suggestions test failed")

if __name__ == '__main__':
    main()
