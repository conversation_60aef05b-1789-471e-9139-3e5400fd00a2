#!/usr/bin/env python3
"""
Test Dr. Resume Suggestions System
Comprehensive test of Local AI and suggestion functionality
"""

import json
import requests
from datetime import datetime

def test_local_ai_direct():
    """Test Local AI service directly"""
    print("🤖 Testing Local AI Service Directly...")
    
    try:
        from services.local_ai_suggestions_service import LocalAISuggestionsService
        
        ai_service = LocalAISuggestionsService()
        
        # Sample data
        resume_keywords = {
            'technical_skills': ['python', 'javascript', 'html', 'css'],
            'soft_skills': ['communication', 'teamwork', 'problem solving'],
            'other_keywords': ['project management', 'agile']
        }
        
        jd_keywords = {
            'technical_skills': ['python', 'react', 'sql', 'docker'],
            'soft_skills': ['communication', 'leadership', 'collaboration'],
            'other_keywords': ['agile', 'scrum', 'ci/cd']
        }
        
        result = ai_service.generate_local_ai_suggestions(
            resume_id=1,
            job_description_id=1,
            resume_keywords=resume_keywords,
            jd_keywords=jd_keywords,
            resume_text="Experienced Python developer with strong communication skills",
            jd_text="Looking for Python developer with React and SQL experience"
        )
        
        print("✅ Local AI Results:")
        print(f"  📊 Analysis: {result['analysis_summary']}")
        print(f"  💡 Recommendations: {len(result['keyword_recommendations'])}")
        print(f"  🚀 Quick Wins: {len(result['quick_wins'])}")
        print(f"  🎯 AI Powered: {result['ai_powered']}")
        print(f"  🏠 Local Processing: {result['local_processing']}")
        
        # Show sample recommendations
        print("\n📋 Sample Recommendations:")
        for i, rec in enumerate(result['keyword_recommendations'][:3], 1):
            print(f"  {i}. {rec}")
        
        return True
        
    except Exception as e:
        print(f"❌ Local AI Error: {e}")
        return False

def test_suggestions_service():
    """Test the main suggestions service"""
    print("\n🔧 Testing Suggestions Service...")
    
    try:
        from services.suggestions_service import SuggestionsService
        
        service = SuggestionsService()
        
        # This would normally require actual database records
        # For now, just test that the service can be instantiated
        print("✅ Suggestions service created successfully")
        
        # Test keyword analysis method
        resume_keywords = {
            'technical_skills': ['python', 'javascript'],
            'soft_skills': ['communication'],
            'other_keywords': ['agile']
        }
        
        jd_keywords = {
            'technical_skills': ['python', 'react'],
            'soft_skills': ['communication', 'leadership'],
            'other_keywords': ['agile', 'scrum']
        }
        
        analysis = service._analyze_keywords(resume_keywords, jd_keywords)
        
        print("✅ Keyword Analysis Results:")
        print(f"  🎯 Matched: {analysis['matched']}")
        print(f"  ❌ Missing: {analysis['missing']}")
        print(f"  🌟 Extra: {analysis['extra']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Suggestions Service Error: {e}")
        return False

def test_premium_service():
    """Test premium suggestions service"""
    print("\n💎 Testing Premium Suggestions Service...")
    
    try:
        from services.premium_suggestions_service import PremiumSuggestionsService
        
        service = PremiumSuggestionsService()
        
        print("✅ Premium service created successfully")
        print(f"  🤖 OpenAI Available: {service.openai_available}")
        print(f"  🏠 Local AI Available: {hasattr(service, 'local_ai')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Premium Service Error: {e}")
        return False

def test_api_endpoints():
    """Test API endpoints if Flask app is running"""
    print("\n🌐 Testing API Endpoints...")
    
    try:
        # Test health endpoint
        response = requests.get('http://localhost:5000/health', timeout=5)
        if response.status_code == 200:
            print("✅ Health endpoint working")
        else:
            print(f"⚠️ Health endpoint returned {response.status_code}")
        
        # Test main page
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code == 200:
            print("✅ Main page accessible")
        else:
            print(f"⚠️ Main page returned {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("⚠️ Flask app not running - skipping API tests")
        return False
    except Exception as e:
        print(f"❌ API Test Error: {e}")
        return False

def main():
    """Run comprehensive suggestions test"""
    print("🩺" + "="*50 + "🩺")
    print("🧪 Dr. Resume - Suggestions System Test")
    print("="*52)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Local AI Direct", test_local_ai_direct),
        ("Suggestions Service", test_suggestions_service),
        ("Premium Service", test_premium_service),
        ("API Endpoints", test_api_endpoints)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*52)
    print("📊 SUGGESTIONS TEST SUMMARY")
    print("="*52)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
    
    print()
    print(f"Overall: {passed}/{total} tests passed")
    
    if passed >= 3:  # Allow API test to fail if app not running
        print("🎉 SUGGESTIONS SYSTEM WORKING!")
        print("🤖 Local AI is generating intelligent recommendations!")
        print("🚀 Dr. Resume is ready for use!")
    else:
        print("⚠️ Some critical issues detected")
        print("🔧 Please check the failing components")
    
    print("\n💡 Next Steps:")
    print("1. Start Flask app: python app.py")
    print("2. Open browser: http://localhost:5000")
    print("3. Test suggestions page")
    print("4. Upload resume and job description")
    print("5. Generate AI suggestions!")
    
    print("🩺" + "="*50 + "🩺")

if __name__ == '__main__':
    main()
